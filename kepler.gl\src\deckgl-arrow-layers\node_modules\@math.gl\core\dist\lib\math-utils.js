// math.gl
// SPDX-License-Identifier: MIT
// Copyright (c) vis.gl contributors
// NOTE: Added to make Cesium-derived test cases work
// TODO: Determine if/how to keep
export const EPSILON1 = 1e-1;
export const EPSILON2 = 1e-2;
export const EPSILON3 = 1e-3;
export const EPSILON4 = 1e-4;
export const EPSILON5 = 1e-5;
export const EPSILON6 = 1e-6;
export const EPSILON7 = 1e-7;
export const EPSILON8 = 1e-8;
export const EPSILON9 = 1e-9;
export const EPSILON10 = 1e-10;
export const EPSILON11 = 1e-11;
export const EPSILON12 = 1e-12;
export const EPSILON13 = 1e-13;
export const EPSILON14 = 1e-14;
export const EPSILON15 = 1e-15;
export const EPSILON16 = 1e-16;
export const EPSILON17 = 1e-17;
export const EPSILON18 = 1e-18;
export const EPSILON19 = 1e-19;
export const EPSILON20 = 1e-20;
export const PI_OVER_TWO = Math.PI / 2;
export const PI_OVER_FOUR = Math.PI / 4;
export const PI_OVER_SIX = Math.PI / 6;
export const TWO_PI = Math.PI * 2;
//# sourceMappingURL=math-utils.js.map