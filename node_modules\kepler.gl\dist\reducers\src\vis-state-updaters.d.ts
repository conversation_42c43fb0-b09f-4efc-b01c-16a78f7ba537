import { ReceiveMapConfigPayload, VisStateActions, MapStateActions, ActionTypes } from '@kepler.gl/actions';
import { Layer } from '@kepler.gl/layers';
import { VisState, Merger, PostMergerPayload } from '@kepler.gl/schemas';
import { Filter, InteractionConfig, AnimationConfig, Editor } from '@kepler.gl/types';
import { Loader } from '@loaders.gl/loader-utils';
import { Datasets } from '@kepler.gl/table';
export declare const defaultInteractionConfig: InteractionConfig;
export declare const DEFAULT_ANIMATION_CONFIG: AnimationConfig;
export declare const DEFAULT_EDITOR: Editor;
/**
 * Default initial `visState`
 * @memberof visStateUpdaters
 * @constant
 * @public
 */
export declare const INITIAL_VIS_STATE: VisState;
declare type UpdateStateWithLayerAndDataType = {
    layers: Layer[];
    layerData: any[];
};
/**
 * Update state with updated layer and layerData
 *
 */
export declare function updateStateWithLayerAndData<S extends UpdateStateWithLayerAndDataType>(state: S, { layerData, layer, idx }: {
    layerData?: any;
    layer: Layer;
    idx: number;
}): S;
export declare function updateStateOnLayerVisibilityChange<S extends VisState>(state: S, layer: Layer): S;
/**
 * Apply layer config
 * @memberof visStateUpdaters
 * @returns nextState
 */
export declare function applyLayerConfigUpdater(state: VisState, action: VisStateActions.ApplyLayerConfigUpdaterAction): VisState;
/**
 * Update layer base config: dataId, label, column, isVisible
 * @memberof visStateUpdaters
 * @returns nextState
 */
export declare function layerConfigChangeUpdater(state: VisState, action: VisStateActions.LayerConfigChangeUpdaterAction): VisState;
/**
 * Updates isValid flag of a layer.
 * Updates isVisible based on the value of isValid.
 * Triggers update of data for the layer in order to get errors again during next update iteration.
 * @memberof visStateUpdaters
 * @returns nextState
 */
export declare function layerSetIsValidUpdater(state: VisState, action: VisStateActions.LayerSetIsValidUpdaterAction): VisState;
/**
 * Update layer base config: dataId, label, column, isVisible
 * @memberof visStateUpdaters
 * @returns nextState
 */
export declare function layerTextLabelChangeUpdater(state: VisState, action: VisStateActions.LayerTextLabelChangeUpdaterAction): VisState;
/**
 * Update layer config dataId
 * @memberof visStateUpdaters
 * @returns nextState
 */
export declare function layerDataIdChangeUpdater(state: VisState, action: {
    oldLayer: Layer;
    newConfig: {
        dataId: string;
    };
}): VisState;
export declare function setInitialLayerConfig(layer: any, datasets: any, layerClasses: any): Layer;
/**
 * Update layer type. Previews layer config will be copied if applicable.
 * @memberof visStateUpdaters
 * @public
 */
export declare function layerTypeChangeUpdater(state: VisState, action: VisStateActions.LayerTypeChangeUpdaterAction): VisState;
/**
 * Update layer visual channel
 * @memberof visStateUpdaters
 * @returns {Object} nextState
 * @public
 */
export declare function layerVisualChannelChangeUpdater(state: VisState, action: VisStateActions.LayerVisualChannelConfigChangeUpdaterAction): VisState;
/**
 * Update layer `visConfig`
 * @memberof visStateUpdaters
 * @public
 */
export declare function layerVisConfigChangeUpdater(state: VisState, action: VisStateActions.LayerVisConfigChangeUpdaterAction): VisState;
/**
 * Update filter property
 * @memberof visStateUpdaters
 * @public
 */
export declare function setFilterAnimationTimeUpdater(state: VisState, action: VisStateActions.SetFilterAnimationTimeUpdaterAction): VisState;
/**
 * Update filter animation window
 * @memberof visStateUpdaters
 * @public
 */
export declare function setFilterAnimationWindowUpdater(state: VisState, { id, animationWindow }: VisStateActions.SetFilterAnimationWindowUpdaterAction): VisState;
/**
 * Update filter property
 * @memberof visStateUpdaters
 * @public
 */
export declare function setFilterUpdater(state: VisState, action: VisStateActions.SetFilterUpdaterAction): VisState;
/**
 * Set the property of a filter plot
 * @memberof visStateUpdaters
 * @public
 */
export declare const setFilterPlotUpdater: (state: VisState, { idx, newProp, valueIndex }: VisStateActions.SetFilterPlotUpdaterAction) => VisState;
/**
 * Add a new filter
 * @memberof visStateUpdaters
 * @public
 */
export declare const addFilterUpdater: (state: VisState, action: VisStateActions.AddFilterUpdaterAction) => VisState;
/**
 * Set layer color palette ui state
 * @memberof visStateUpdaters
 */
export declare const layerColorUIChangeUpdater: (state: VisState, { oldLayer, prop, newConfig }: VisStateActions.LayerColorUIChangeUpdaterAction) => VisState;
/**
 * Start and end filter animation
 * @memberof visStateUpdaters
 * @public
 */
export declare const toggleFilterAnimationUpdater: (state: VisState, action: VisStateActions.ToggleFilterAnimationUpdaterAction) => VisState;
/**
 * @memberof visStateUpdaters
 * @public
 */
export declare const toggleLayerAnimationUpdater: (state: VisState, action: VisStateActions.ToggleLayerAnimationUpdaterAction) => VisState;
/**
 * Hide and show layer animation control
 * @memberof visStateUpdaters
 * @public
 */
export declare const toggleLayerAnimationControlUpdater: (state: VisState, action: VisStateActions.ToggleLayerAnimationControlUpdaterAction) => VisState;
/**
 * Change filter animation speed
 * @memberof visStateUpdaters
 * @public
 */
export declare const updateFilterAnimationSpeedUpdater: (state: VisState, action: VisStateActions.UpdateFilterAnimationSpeedUpdaterAction) => VisState;
/**
 * Reset animation config current time to a specified value
 * @memberof visStateUpdaters
 * @public
 *
 */
export declare const setLayerAnimationTimeUpdater: (state: VisState, { value }: VisStateActions.SetLayerAnimationTimeUpdaterAction) => VisState;
/**
 * Update animation speed with the vertical speed slider
 * @memberof visStateUpdaters
 * @public
 *
 */
export declare const updateLayerAnimationSpeedUpdater: (state: VisState, { speed }: VisStateActions.UpdateLayerAnimationSpeedUpdaterAction) => VisState;
/**
 * Show larger time filter at bottom for time playback (apply to time filter only)
 * @memberof visStateUpdaters
 * @public
 */
export declare const setFilterViewUpdater: (state: VisState, action: VisStateActions.SetFilterViewUpdaterAction) => {
    filters: Filter[];
    mapInfo: import("@kepler.gl/types").MapInfo;
    layers: Layer[];
    layerData: any[];
    layerToBeMerged: any[];
    layerOrder: string[];
    effects: import("@kepler.gl/types").Effect[];
    effectOrder: string[];
    filterToBeMerged: any[];
    datasets: Datasets;
    editingDataset: string | undefined;
    interactionConfig: InteractionConfig;
    interactionToBeMerged: any;
    layerBlending: string;
    overlayBlending?: string | undefined;
    hoverInfo: any;
    clicked: any;
    mousePos: any;
    maxDefaultTooltips: number;
    layerClasses: {
        point: typeof import("@kepler.gl/layers/dist/point-layer/point-layer").default;
        arc: typeof import("@kepler.gl/layers/dist/arc-layer/arc-layer").default;
        line: typeof import("@kepler.gl/layers/dist/line-layer/line-layer").default;
        grid: typeof import("@kepler.gl/layers/dist/grid-layer/grid-layer").default;
        hexagon: typeof import("@kepler.gl/layers/dist/hexagon-layer/hexagon-layer").default;
        geojson: typeof import("@kepler.gl/layers/dist/geojson-layer/geojson-layer").default;
        cluster: typeof import("@kepler.gl/layers/dist/cluster-layer/cluster-layer").default;
        icon: typeof import("@kepler.gl/layers/dist/icon-layer/icon-layer").default;
        heatmap: typeof import("@kepler.gl/layers/dist/heatmap-layer/heatmap-layer").default;
        hexagonId: typeof import("@kepler.gl/layers/dist/h3-hexagon-layer/h3-hexagon-layer").default;
        "3D": typeof import("@kepler.gl/layers").ScenegraphLayer;
        trip: typeof import("@kepler.gl/layers/dist/trip-layer/trip-layer").default;
        s2: typeof import("@kepler.gl/layers/dist/s2-geometry-layer/s2-geometry-layer").default;
    };
    animationConfig: AnimationConfig;
    editor: Editor;
    splitMaps: import("@kepler.gl/types").SplitMap[];
    splitMapsToBeMerged: import("@kepler.gl/types").SplitMap[];
    fileLoading: false | import("@kepler.gl/types").FileLoading;
    fileLoadingProgress: import("@kepler.gl/types").FileLoadingProgress;
    loaders: Loader<any, any, import("@loaders.gl/loader-utils").LoaderOptions>[];
    loadOptions: object;
    initialState?: Partial<VisState> | undefined;
    mergers: import("@kepler.gl/schemas").VisStateMergers<any>;
    schema: import("@kepler.gl/schemas").KeplerGLSchemaClass;
    preserveLayerOrder?: string[] | undefined;
    preserveFilterOrder?: string[] | undefined;
    preserveDatasetOrder?: string[] | undefined;
    isMergingDatasets: {
        [datasetId: string]: boolean;
    };
};
/**
 * Toggles filter feature visibility
 * @memberof visStateUpdaters
 */
export declare const toggleFilterFeatureUpdater: (state: VisState, action: VisStateActions.ToggleFilterFeatureUpdaterAction) => VisState;
/**
 * Remove a filter
 * @memberof visStateUpdaters
 * @public
 */
export declare const removeFilterUpdater: (state: VisState, action: VisStateActions.RemoveFilterUpdaterAction) => VisState;
/**
 * Add a new layer
 * @memberof visStateUpdaters
 * @public
 */
export declare const addLayerUpdater: (state: VisState, action: VisStateActions.AddLayerUpdaterAction) => VisState;
/**
 * remove layer
 * @memberof visStateUpdaters
 * @public
 */
export declare function removeLayerUpdater<T extends VisState>(state: T, { id }: VisStateActions.RemoveLayerUpdaterAction): T;
/**
 * Reorder layer
 * @memberof visStateUpdaters
 * @public
 */
export declare const reorderLayerUpdater: (state: VisState, { order }: VisStateActions.ReorderLayerUpdaterAction) => VisState;
/**
 * duplicate layer
 * @memberof visStateUpdaters
 * @public
 */
export declare const duplicateLayerUpdater: (state: VisState, { id }: VisStateActions.DuplicateLayerUpdaterAction) => VisState;
/**
 * Add a new effect
 * @memberof visStateUpdaters
 * @public
 */
export declare const addEffectUpdater: (state: VisState, action: VisStateActions.AddEffectUpdaterAction) => VisState;
/**
 * remove effect
 * @memberof visStateUpdaters
 * @public
 */
export declare const removeEffectUpdater: (state: VisState, { id }: VisStateActions.RemoveEffectUpdaterAction) => VisState;
/**
 * Reorder effect
 * @memberof visStateUpdaters
 * @public
 */
export declare const reorderEffectUpdater: (state: VisState, { order }: VisStateActions.ReorderEffectUpdaterAction) => VisState;
/**
 * Update effect
 * @memberof visStateUpdaters
 * @public
 */
export declare const updateEffectUpdater: (state: VisState, { id, props }: VisStateActions.UpdateEffectUpdaterAction) => VisState;
/**
 * Remove a dataset and all layers, filters, tooltip configs that based on it
 * @memberof visStateUpdaters
 * @public
 */
export declare function removeDatasetUpdater<T extends VisState>(state: T, action: VisStateActions.RemoveDatasetUpdaterAction): T;
/**
 * update layer blending mode
 * @memberof visStateUpdaters
 * @public
 */
export declare const updateLayerBlendingUpdater: (state: VisState, action: VisStateActions.UpdateLayerBlendingUpdaterAction) => VisState;
/**
 * update overlay blending mode
 * @memberof visStateUpdaters
 * @public
 */
export declare const updateOverlayBlendingUpdater: (state: VisState, action: VisStateActions.UpdateOverlayBlendingUpdaterAction) => VisState;
/**
 * Display dataset table in a modal
 * @memberof visStateUpdaters
 * @public
 */
export declare const showDatasetTableUpdater: (state: VisState, action: VisStateActions.ShowDatasetTableUpdaterAction) => VisState;
/**
 * Add custom color for datasets and layers
 * @memberof visStateUpdaters
 * @public
 */
export declare const updateTableColorUpdater: (state: VisState, action: VisStateActions.UpdateDatasetColorUpdater) => VisState;
/**
 * reset visState to initial State
 * @memberof visStateUpdaters
 * @public
 */
export declare const resetMapConfigUpdater: (state: VisState) => VisState;
/**
 * Propagate `visState` reducer with a new configuration. Current config will be override.
 * @memberof visStateUpdaters
 * @public
 */
export declare const receiveMapConfigUpdater: (state: VisState, { payload: { config, options } }: {
    type?: typeof ActionTypes.RECEIVE_MAP_CONFIG;
    payload: ReceiveMapConfigPayload;
}) => VisState;
/**
 * Trigger layer hover event with hovered object
 * @memberof visStateUpdaters
 * @public
 */
export declare const layerHoverUpdater: (state: VisState, action: VisStateActions.OnLayerHoverUpdaterAction) => VisState;
/**
 * Update `interactionConfig`
 * @memberof visStateUpdaters
 * @public
 */
export declare function interactionConfigChangeUpdater(state: VisState, action: VisStateActions.InteractionConfigChangeUpdaterAction): VisState;
/**
 * Trigger layer click event with clicked object
 * @memberof visStateUpdaters
 * @public
 */
export declare const layerClickUpdater: (state: VisState, action: VisStateActions.OnLayerClickUpdaterAction) => VisState;
/**
 * Trigger map click event, unselect clicked object
 * @memberof visStateUpdaters
 * @public
 */
export declare const mapClickUpdater: (state: VisState, action: VisStateActions.OnMapClickUpdaterAction) => VisState;
/**
 * Trigger map move event
 * @memberof visStateUpdaters
 * @public
 */
export declare const mouseMoveUpdater: (state: VisState, { evt }: VisStateActions.OnMouseMoveUpdaterAction) => VisState;
/**
 * Toggle visibility of a layer for a split map
 * @memberof visStateUpdaters
 * @public
 */
export declare const toggleSplitMapUpdater: (state: VisState, action: MapStateActions.ToggleSplitMapUpdaterAction) => VisState;
/**
 * Toggle visibility of a layer in a split map
 * @memberof visStateUpdaters
 * @public
 */
export declare const toggleLayerForMapUpdater: (state: VisState, { mapIndex, layerId }: VisStateActions.ToggleLayerForMapUpdaterAction) => VisState;
/**
 * Add new dataset to `visState`, with option to load a map config along with the datasets
 * @memberof visStateUpdaters
 * @public
 */
export declare const updateVisDataUpdater: (state: VisState, action: VisStateActions.UpdateVisDataUpdaterAction) => VisState;
/**
 * Add new dataset to `visState`, with option to load a map config along with the datasets
 */
export declare function applyMergersUpdater(state: VisState, action: {
    mergers: Merger<any>[];
    postMergerPayload: PostMergerPayload;
}): VisState;
/**
 * Rename an existing dataset in `visState`
 * @memberof visStateUpdaters
 * @public
 */
export declare function renameDatasetUpdater(state: VisState, action: VisStateActions.RenameDatasetUpdaterAction): VisState;
/**
 * Update Dataset props (label, color, meta). Do not use to update data or any related properties
 * @memberof visStateUpdaters
 * @public
 */
export declare function updateDatasetPropsUpdater(state: VisState, action: VisStateActions.UpdateDatasetPropsUpdaterAction): VisState;
/**
 * When a user clicks on the specific map closing icon
 * the application will close the selected map
 * and will merge the remaining one with the global state
 * TODO: i think in the future this action should be called merge map layers with global settings
 * @param {Object} state `visState`
 * @param {Object} action action
 * @returns {Object} nextState
 */
export declare function closeSpecificMapAtIndex<S extends VisState>(state: S, action: MapStateActions.ToggleSplitMapUpdaterAction): S;
/**
 * Trigger file loading dispatch `addDataToMap` if succeed, or `loadFilesErr` if failed
 * @memberof visStateUpdaters
 * @public
 */
export declare const loadFilesUpdater: (state: VisState, action: VisStateActions.LoadFilesUpdaterAction) => VisState;
/**
 * Sucessfully loaded one file, move on to the next one
 * @memberof visStateUpdaters
 * @public
 */
export declare function loadFileStepSuccessUpdater(state: VisState, action: VisStateActions.LoadFileStepSuccessAction): VisState;
/**
 *
 * @memberof visStateUpdaters
 * @public
 */
export declare function loadNextFileUpdater(state: VisState): VisState;
export declare function makeLoadFileTask(file: any, fileCache: any, loaders?: Loader[], loadOptions?: {}): any;
/**
 *
 * @memberof visStateUpdaters
 * @public
 */
export declare function processFileContentUpdater(state: VisState, action: VisStateActions.ProcessFileContentUpdaterAction): VisState;
export declare function parseProgress(prevProgress: {} | undefined, progress: any): {
    percent?: undefined;
} | {
    percent: any;
};
/**
 * gets called with payload = AsyncGenerator<???>
 * @memberof visStateUpdaters
 * @public
 */
export declare const nextFileBatchUpdater: (state: VisState, { payload: { gen, fileName, progress, accumulated, onFinish } }: VisStateActions.NextFileBatchUpdaterAction) => VisState;
/**
 * Trigger loading file error
 * @memberof visStateUpdaters
 * @public
 */
export declare const loadFilesErrUpdater: (state: VisState, { error, fileName }: VisStateActions.LoadFilesErrUpdaterAction) => VisState;
/**
 * When select dataset for export, apply cpu filter to selected dataset
 * @memberof visStateUpdaters
 * @public
 */
export declare const applyCPUFilterUpdater: (state: VisState, { dataId }: VisStateActions.ApplyCPUFilterUpdaterAction) => VisState;
/**
 * User input to update the info of the map
 * @memberof visStateUpdaters
 * @public
 */
export declare const setMapInfoUpdater: (state: VisState, action: VisStateActions.SetMapInfoUpdaterAction) => VisState;
/**
 * Helper function to update All layer domain and layer data of state
 */
export declare function addDefaultLayers(state: VisState, datasets: Datasets): {
    state: VisState;
    newLayers: Layer[];
};
/**
 * helper function to find default tooltips
 * @param {Object} state
 * @param {Object} dataset
 * @returns {Object} nextState
 */
export declare function addDefaultTooltips(state: any, dataset: any): any;
export declare function initialFileLoadingProgress(file: any, index: any): {
    [x: number]: {
        percent: number;
        message: string;
        fileName: any;
        error: null;
    };
};
export declare function updateFileLoadingProgressUpdater(state: any, { fileName, progress }: {
    fileName: any;
    progress: any;
}): any;
/**
 * Helper function to update layer domains for an array of datasets
 */
export declare function updateAllLayerDomainData(state: VisState, dataId: string | string[], updatedFilter?: Filter): VisState;
export declare function updateAnimationDomain<S extends VisState>(state: S): S;
/**
 * Update the status of the editor
 * @memberof visStateUpdaters
 */
export declare const setEditorModeUpdater: (state: VisState, { mode }: VisStateActions.SetEditorModeUpdaterAction) => VisState;
/**
 * Update editor features
 * @memberof visStateUpdaters
 */
export declare function setFeaturesUpdater(state: VisState, { features }: VisStateActions.SetFeaturesUpdaterAction): VisState;
/**
 * Set the current selected feature
 * @memberof uiStateUpdaters
 */
export declare const setSelectedFeatureUpdater: (state: VisState, { feature, selectionContext }: VisStateActions.SetSelectedFeatureUpdaterAction) => VisState;
/**
 * Delete existing feature from filters
 * @memberof visStateUpdaters
 */
export declare function deleteFeatureUpdater(state: VisState, { feature }: VisStateActions.DeleteFeatureUpdaterAction): VisState;
/**
 * Toggle feature as layer filter
 * @memberof visStateUpdaters
 */
export declare function setPolygonFilterLayerUpdater(state: VisState, payload: VisStateActions.SetPolygonFilterLayerUpdaterAction): VisState;
/**
 * @memberof visStateUpdaters
 * @public
 */
export declare function sortTableColumnUpdater(state: VisState, { dataId, column, mode }: VisStateActions.SortTableColumnUpdaterAction): VisState;
/**
 * @memberof visStateUpdaters
 * @public
 */
export declare function pinTableColumnUpdater(state: VisState, { dataId, column }: VisStateActions.PinTableColumnUpdaterAction): VisState;
/**
 * Copy column content as strings
 * @memberof visStateUpdaters
 * @public
 */
export declare function copyTableColumnUpdater(state: VisState, { dataId, column }: VisStateActions.CopyTableColumnUpdaterAction): VisState;
/**
 * Set display format from columns from user selection
 * @memberof visStateUpdaters
 * @public
 */
export declare function setColumnDisplayFormatUpdater(state: VisState, { dataId, formats }: VisStateActions.SetColumnDisplayFormatUpdaterAction): VisState;
/**
 * Update editor
 */
export declare function toggleEditorVisibilityUpdater(state: VisState, action: VisStateActions.ToggleEditorVisibilityUpdaterAction): VisState;
export declare function setFilterAnimationTimeConfigUpdater(state: VisState, { idx, config }: VisStateActions.SetFilterAnimationTimeConfigAction): VisState;
/**
 * Update editor
 */
export declare function setLayerAnimationTimeConfigUpdater(state: VisState, { config }: VisStateActions.SetLayerAnimationTimeConfigAction): VisState;
export declare function prepareStateForDatasetReplace<T extends VisState>(state: T, dataId: string, dataIdToUse: string): T;
export declare function replaceDatasetDepsInState<T extends VisState>(state: T, { dataId, dataIdToUse }: {
    dataId: string;
    dataIdToUse: string;
}): T;
export {};
