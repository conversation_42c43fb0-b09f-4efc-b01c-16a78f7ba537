{"name": "@turf/helpers", "version": "7.2.0", "description": "turf helpers module", "author": "Turf Authors", "contributors": ["<PERSON> <@tmcw>", "<PERSON> <@stebogit>", "<PERSON> <@DenisCarriere>", "<PERSON> <@wnordmann>"], "license": "MIT", "bugs": {"url": "https://github.com/Turfjs/turf/issues"}, "homepage": "https://github.com/Turfjs/turf", "repository": {"type": "git", "url": "git://github.com/Turfjs/turf.git"}, "funding": "https://opencollective.com/turf", "publishConfig": {"access": "public"}, "keywords": ["geo", "point", "turf", "g<PERSON><PERSON><PERSON>"], "type": "module", "main": "dist/cjs/index.cjs", "module": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}}, "sideEffects": false, "files": ["dist"], "scripts": {"bench": "tsx bench.ts", "build": "tsup --config ../../tsup.config.ts", "docs": "tsx ../../scripts/generate-readmes.ts", "test": "npm-run-all --npm-path npm test:*", "test:tape": "tsx test.ts", "test:types": "tsc --esModuleInterop --module node16 --moduleResolution node16 --noEmit --strict types.ts"}, "devDependencies": {"@types/benchmark": "^2.1.5", "@types/tape": "^4.13.4", "benchmark": "^2.1.4", "npm-run-all": "^4.1.5", "tape": "^5.9.0", "tsup": "^8.3.5", "tsx": "^4.19.2", "typescript": "^5.5.4"}, "dependencies": {"@types/geojson": "^7946.0.10", "tslib": "^2.8.1"}, "gitHead": "7b0f0374c4668cd569f8904c71e2ae7d941be867"}