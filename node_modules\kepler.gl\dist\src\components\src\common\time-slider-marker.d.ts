import React from 'react';
import { NumberValue } from 'd3-scale';
export declare function getTickFormat(timezone: string): (date: any) => string;
export declare function getXAxis(domain: Date[] | NumberValue[], width: number, isEnlarged: boolean, timezone: string): any;
export declare function updateAxis(xAxisRef: any, xAxis: any): void;
interface TimeSliderMarkerProps {
    width: number;
    domain: Date[] | NumberValue[];
    isEnlarged?: boolean;
    height?: number;
    timezone: string;
}
declare function TimeSliderMarkerFactory(): React.MemoExoticComponent<({ width, domain, isEnlarged, height, timezone }: TimeSliderMarkerProps) => React.JSX.Element>;
export default TimeSliderMarkerFactory;
