"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.updateMap = exports.toggleSplitMapViewport = exports.toggleSplitMap = exports.togglePerspective = exports.fitBounds = void 0;
var _toolkit = require("@reduxjs/toolkit");
var _actionTypes = _interopRequireDefault(require("./action-types"));
// SPDX-License-Identifier: MIT
// Copyright contributors to the kepler.gl project

/**
 *
 * Toggle between 3d and 2d map.
 * @memberof mapStateActions
 * @public
 * @example
 * import {togglePerspective} from '@kepler.gl/actions';
 * this.props.dispatch(togglePerspective());
 */
var togglePerspective = exports.togglePerspective = (0, _toolkit.createAction)(_actionTypes["default"].TOGGLE_PERSPECTIVE);
/**
 * Fit map viewport to bounds
 * @memberof mapStateActions
 * @param {Array<Number>} bounds as `[lngMin, latMin, lngMax, latMax]`
 * @public
 * @example
 * import {fitBounds} from '@kepler.gl/actions';
 * this.props.dispatch(fitBounds([-122.23, 37.127, -122.11, 37.456]));
 */
var fitBounds = exports.fitBounds = (0, _toolkit.createAction)(_actionTypes["default"].FIT_BOUNDS, function (bounds) {
  return {
    payload: bounds
  };
});
/**
 * Update map viewport
 * @memberof mapStateActions
 * @param {Object} viewport viewport object container one or any of these properties `width`, `height`, `latitude` `longitude`, `zoom`, `pitch`, `bearing`, `dragRotate`
 * @param {Number} [viewport.width] Width of viewport
 * @param {Number} [viewport.height] Height of viewport
 * @param {Number} [viewport.zoom] Zoom of viewport
 * @param {Number} [viewport.pitch] Camera angle in degrees (0 is straight down)
 * @param {Number} [viewport.bearing] Map rotation in degrees (0 means north is up)
 * @param {Number} [viewport.latitude] Latitude center of viewport on map in mercator projection
 * @param {Number} [viewport.longitude] Longitude Center of viewport on map in mercator projection
 * @param {boolean} [viewport.dragRotate] Whether to enable drag and rotate map into perspective viewport
 * @param {number} mapIndex Index of which map to update the viewport of
 * @public
 * @example
 * import {updateMap} from '@kepler.gl/actions';
 * this.props.dispatch(updateMap({latitude: 37.75043, longitude: -122.34679, width: 800, height: 1200}, 0));
 */

var updateMap = exports.updateMap = (0, _toolkit.createAction)(_actionTypes["default"].UPDATE_MAP, function (viewport, mapIndex) {
  return {
    payload: {
      viewport: viewport,
      mapIndex: mapIndex
    }
  };
});
/**
 * Toggle between single map or split maps
 * @memberof mapStateActions
 * @param {Number} [index] index is provided, close split map at index
 * @public
 * @example
 * import {toggleSplitMap} from '@kepler.gl/actions';
 * this.props.dispatch(toggleSplitMap());
 */
var toggleSplitMap = exports.toggleSplitMap = (0, _toolkit.createAction)(_actionTypes["default"].TOGGLE_SPLIT_MAP, function (index) {
  return {
    payload: index
  };
});
/**
 * For split maps, toggle between having (un)synced viewports and (un)locked zooms
 * @memberof mapStateActions
 * @param {Object} syncInfo
 * @param {boolean} [syncInfo.isViewportSynced] Are the 2 split maps having synced viewports?
 * @param {boolean} [syncInfo.isZoomLocked] If split, are the zooms locked to each other or independent?
 */
var toggleSplitMapViewport = exports.toggleSplitMapViewport = (0, _toolkit.createAction)(_actionTypes["default"].TOGGLE_SPLIT_MAP_VIEWPORT, function (syncInfo) {
  return {
    payload: syncInfo
  };
});

/**
 * This declaration is needed to group actions in docs
 */
/**
 * Actions handled mostly by  `mapState` reducer.
 * They manage map viewport update, toggle between 2d and 3d map,
 * toggle between single and split maps.
 *
 * @public
 */
/* eslint-disable @typescript-eslint/no-unused-vars */
// @ts-ignore
var mapStateActions = null;
/* eslint-enable @typescript-eslint/no-unused-vars */
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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