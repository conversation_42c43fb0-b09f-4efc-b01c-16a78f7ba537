import React, { Component, CSSProperties } from 'react';
export declare type BaseProps = {
    /** Set the height of the icon, ex. '16px' */
    height?: string;
    /** Set the width of the icon, ex. '16px' */
    width?: string;
    /** Set the viewbox of the svg */
    viewBox?: string;
    /** Path element */
    predefinedClassName?: string;
    className?: string;
    style?: CSSProperties;
    colors?: string[];
    totalColor?: number;
} & React.SVGAttributes<SVGSVGElement> & React.DOMAttributes<SVGSVGElement>;
export declare class Base extends Component<BaseProps> {
    static displayName: string;
    static defaultProps: {
        height: null;
        width: null;
        viewBox: string;
        predefinedClassName: string;
        className: string;
        style: {
            fill: string;
        };
    };
    render(): React.JSX.Element;
}
