import { ComponentType, ElementType } from 'react';
import RangePlotFactory from './range-plot';
import RangeSliderSubAnimationPanelFactory from '../common/range-slider-timeline-panel';
import { LineChart, Filter, Bins } from '@kepler.gl/types';
import { Datasets } from '@kepler.gl/table';
import { ActionHandler, setFilterPlot } from '@kepler.gl/actions';
interface RangeSliderProps {
    range?: number[];
    value0: number;
    value1: number;
    onChange?: (val: number[], e?: Event | null) => void;
    setFilterPlot?: ActionHandler<typeof setFilterPlot>;
    bins?: Bins;
    isRanged?: boolean;
    isEnlarged?: boolean;
    showInput?: boolean;
    inputTheme?: string;
    inputSize?: string;
    step?: number;
    sliderHandleWidth?: number;
    xAxis?: ElementType;
    subAnimations?: any[];
    timelineLabel?: string;
    timezone?: string | null;
    timeFormat?: string;
    playbackControlWidth?: number;
    lineChart?: LineChart;
    marks?: number[];
    plotType?: {
        [key: string]: any;
    };
    plotValue?: number[];
    animationWindow?: string;
    filter?: Filter;
    datasets?: Datasets;
    invertTrendColor?: boolean;
}
declare function RangeSliderFactory(RangePlot: ReturnType<typeof RangePlotFactory>, RangeSliderSubAnimationPanel: ReturnType<typeof RangeSliderSubAnimationPanelFactory>): ComponentType<RangeSliderProps>;
declare namespace RangeSliderFactory {
    var deps: (typeof RangePlotFactory | typeof RangeSliderSubAnimationPanelFactory)[];
}
export default RangeSliderFactory;
