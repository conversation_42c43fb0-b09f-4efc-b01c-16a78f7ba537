"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, "default"), secondTarget && __copyProps(secondTarget, mod, "default"));
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));

// ../worker-utils/src/lib/node/worker_threads.ts
var worker_threads_exports = {};
__export(worker_threads_exports, {
  NodeWorker: () => NodeWorker,
  parentPort: () => parentPort
});
var WorkerThreads = __toESM(require("worker_threads"), 1);
__reExport(worker_threads_exports, require("worker_threads"));
var parentPort = WorkerThreads == null ? void 0 : WorkerThreads.parentPort;
var NodeWorker = WorkerThreads.Worker;

// ../worker-utils/src/lib/worker-utils/get-transfer-list.ts
function getTransferList(object, recursive = true, transfers) {
  const transfersSet = transfers || /* @__PURE__ */ new Set();
  if (!object) {
  } else if (isTransferable(object)) {
    transfersSet.add(object);
  } else if (isTransferable(object.buffer)) {
    transfersSet.add(object.buffer);
  } else if (ArrayBuffer.isView(object)) {
  } else if (recursive && typeof object === "object") {
    for (const key in object) {
      getTransferList(object[key], recursive, transfersSet);
    }
  }
  return transfers === void 0 ? Array.from(transfersSet) : [];
}
function isTransferable(object) {
  if (!object) {
    return false;
  }
  if (object instanceof ArrayBuffer) {
    return true;
  }
  if (typeof MessagePort !== "undefined" && object instanceof MessagePort) {
    return true;
  }
  if (typeof ImageBitmap !== "undefined" && object instanceof ImageBitmap) {
    return true;
  }
  if (typeof OffscreenCanvas !== "undefined" && object instanceof OffscreenCanvas) {
    return true;
  }
  return false;
}

// ../worker-utils/src/lib/worker-farm/worker-body.ts
async function getParentPort() {
  return parentPort;
}
var onMessageWrapperMap = /* @__PURE__ */ new Map();
var WorkerBody = class {
  /** Check that we are actually in a worker thread */
  static async inWorkerThread() {
    return typeof self !== "undefined" || Boolean(await getParentPort());
  }
  /*
   * (type: WorkerMessageType, payload: WorkerMessagePayload) => any
   */
  static set onmessage(onMessage) {
    async function handleMessage(message) {
      const parentPort2 = await getParentPort();
      const { type, payload } = parentPort2 ? message : message.data;
      onMessage(type, payload);
    }
    getParentPort().then((parentPort2) => {
      if (parentPort2) {
        parentPort2.on("message", (message) => {
          handleMessage(message);
        });
        parentPort2.on("exit", () => console.debug("Node worker closing"));
      } else {
        globalThis.onmessage = handleMessage;
      }
    });
  }
  static async addEventListener(onMessage) {
    let onMessageWrapper = onMessageWrapperMap.get(onMessage);
    if (!onMessageWrapper) {
      onMessageWrapper = async (message) => {
        if (!isKnownMessage(message)) {
          return;
        }
        const parentPort3 = await getParentPort();
        const { type, payload } = parentPort3 ? message : message.data;
        onMessage(type, payload);
      };
    }
    const parentPort2 = await getParentPort();
    if (parentPort2) {
      console.error("not implemented");
    } else {
      globalThis.addEventListener("message", onMessageWrapper);
    }
  }
  static async removeEventListener(onMessage) {
    const onMessageWrapper = onMessageWrapperMap.get(onMessage);
    onMessageWrapperMap.delete(onMessage);
    const parentPort2 = await getParentPort();
    if (parentPort2) {
      console.error("not implemented");
    } else {
      globalThis.removeEventListener("message", onMessageWrapper);
    }
  }
  /**
   * Send a message from a worker to creating thread (main thread)
   * @param type
   * @param payload
   */
  static async postMessage(type, payload) {
    const data = { source: "loaders.gl", type, payload };
    const transferList = getTransferList(payload);
    const parentPort2 = await getParentPort();
    if (parentPort2) {
      parentPort2.postMessage(data, transferList);
    } else {
      globalThis.postMessage(data, transferList);
    }
  }
};
function isKnownMessage(message) {
  const { type, data } = message;
  return type === "message" && data && typeof data.source === "string" && data.source.startsWith("loaders.gl");
}

// ../loader-utils/src/lib/worker-loader-utils/create-loader-worker.ts
var requestId = 0;
async function createLoaderWorker(loader) {
  if (!await WorkerBody.inWorkerThread()) {
    return;
  }
  WorkerBody.onmessage = async (type, payload) => {
    switch (type) {
      case "process":
        try {
          const { input, options = {}, context = {} } = payload;
          const result = await parseData({
            loader,
            arrayBuffer: input,
            options,
            // @ts-expect-error fetch missing
            context: {
              ...context,
              _parse: parseOnMainThread
            }
          });
          WorkerBody.postMessage("done", { result });
        } catch (error) {
          const message = error instanceof Error ? error.message : "";
          WorkerBody.postMessage("error", { error: message });
        }
        break;
      default:
    }
  };
}
function parseOnMainThread(arrayBuffer, loader, options, context) {
  return new Promise((resolve, reject) => {
    const id = requestId++;
    const onMessage = (type, payload2) => {
      if (payload2.id !== id) {
        return;
      }
      switch (type) {
        case "done":
          WorkerBody.removeEventListener(onMessage);
          resolve(payload2.result);
          break;
        case "error":
          WorkerBody.removeEventListener(onMessage);
          reject(payload2.error);
          break;
        default:
      }
    };
    WorkerBody.addEventListener(onMessage);
    const payload = { id, input: arrayBuffer, options };
    WorkerBody.postMessage("process", payload);
  });
}
async function parseData({
  loader,
  arrayBuffer,
  options,
  context
}) {
  let data;
  let parser;
  if (loader.parseSync || loader.parse) {
    data = arrayBuffer;
    parser = loader.parseSync || loader.parse;
  } else if (loader.parseTextSync) {
    const textDecoder = new TextDecoder();
    data = textDecoder.decode(arrayBuffer);
    parser = loader.parseTextSync;
  } else {
    throw new Error(`Could not load data with ${loader.name} loader`);
  }
  options = {
    ...options,
    modules: loader && loader.options && loader.options.modules || {},
    worker: false
  };
  return await parser(data, { ...options }, context, loader);
}

// src/null-loader.ts
var VERSION = true ? "4.3.2" : "latest";
var NullLoader = {
  dataType: null,
  batchType: null,
  name: "Null loader",
  id: "null",
  module: "core",
  version: VERSION,
  mimeTypes: ["application/x.empty"],
  extensions: ["null"],
  parse: async (arrayBuffer, options, context) => parseSync(arrayBuffer, options || {}, context),
  parseSync,
  parseInBatches: async function* generator(asyncIterator, options, context) {
    for await (const batch of asyncIterator) {
      yield parseSync(batch, options, context);
    }
  },
  tests: [() => false],
  options: {
    null: {}
  }
};
function parseSync(arrayBuffer, options, context) {
  return null;
}

// src/workers/null-worker.ts
createLoaderWorker(NullLoader);
