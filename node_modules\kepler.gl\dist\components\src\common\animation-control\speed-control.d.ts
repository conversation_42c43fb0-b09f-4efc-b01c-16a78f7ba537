import React from 'react';
declare function SpeedControlFactory(AnimationSpeedSlider: any): ({ showAnimationWindowControl, updateAnimationSpeed, btnStyle, hideAndShowSpeedControl, buttonHeight, playbackIcons, speed, isSpeedControlVisible }: {
    showAnimationWindowControl: any;
    updateAnimationSpeed: any;
    btnStyle: any;
    hideAndShowSpeedControl: any;
    buttonHeight: any;
    playbackIcons: any;
    speed: any;
    isSpeedControlVisible: any;
}) => React.JSX.Element | null;
export default SpeedControlFactory;
