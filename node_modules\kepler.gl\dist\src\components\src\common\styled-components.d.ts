/// <reference types="react" />
import { RGBColor } from '@kepler.gl/types';
import { TooltipProps } from 'react-tooltip';
import { IStyledComponent } from 'styled-components';
import { BaseComponentProps } from '../types';
export declare function shouldForwardProp(propName: any, target: any): boolean;
export declare const SelectText: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLSpanElement>, HTMLSpanElement>, never>>;
export declare const SelectTextBold: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<Omit<import("styled-components/dist/types").FastOmit<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLSpanElement>, HTMLSpanElement>, never>, "ref"> & {
    ref?: ((instance: HTMLSpanElement | null) => void) | import("react").RefObject<HTMLSpanElement> | null | undefined;
}, never>> & Omit<IStyledComponent<"web", import("styled-components/dist/types").FastOmit<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLSpanElement>, HTMLSpanElement>, never>>, keyof import("react").Component<any, {}, any>>;
export declare const IconRoundSmall: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, never>>;
export declare const CenterFlexbox: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, never>>;
export declare const CenterVerticalFlexbox: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, never>>;
export declare const EndHorizontalFlexbox: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, never>>;
export declare const SpaceBetweenFlexbox: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, never>>;
export declare const SBFlexboxItem: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, never>>;
export declare const SBFlexboxNoMargin: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, never>>;
export declare const PanelLabel: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<import("styled-components/dist/types").FastOmit<import("styled-components/dist/types").Substitute<import("react").DetailedHTMLProps<import("react").LabelHTMLAttributes<HTMLLabelElement>, HTMLLabelElement>, Omit<import("react").DetailedHTMLProps<import("react").LabelHTMLAttributes<HTMLLabelElement>, HTMLLabelElement>, "ref"> & {
    ref?: ((instance: HTMLLabelElement | null) => void) | import("react").RefObject<HTMLLabelElement> | null | undefined;
}>, never>, never>>;
export declare const PanelLabelWrapper: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<import("styled-components/dist/types").FastOmit<import("styled-components/dist/types").Substitute<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, Omit<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, "ref"> & {
    ref?: ((instance: HTMLDivElement | null) => void) | import("react").RefObject<HTMLDivElement> | null | undefined;
}>, never>, never>>;
export declare const PanelLabelBold: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<import("styled-components/dist/types").FastOmit<import("styled-components/dist/types").FastOmit<import("styled-components/dist/types").Substitute<import("react").DetailedHTMLProps<import("react").LabelHTMLAttributes<HTMLLabelElement>, HTMLLabelElement>, Omit<import("react").DetailedHTMLProps<import("react").LabelHTMLAttributes<HTMLLabelElement>, HTMLLabelElement>, "ref"> & {
    ref?: ((instance: HTMLLabelElement | null) => void) | import("react").RefObject<HTMLLabelElement> | null | undefined;
}>, never>, never>, never>> & Omit<IStyledComponent<"web", import("styled-components/dist/types").FastOmit<import("styled-components/dist/types").FastOmit<import("styled-components/dist/types").Substitute<import("react").DetailedHTMLProps<import("react").LabelHTMLAttributes<HTMLLabelElement>, HTMLLabelElement>, Omit<import("react").DetailedHTMLProps<import("react").LabelHTMLAttributes<HTMLLabelElement>, HTMLLabelElement>, "ref"> & {
    ref?: ((instance: HTMLLabelElement | null) => void) | import("react").RefObject<HTMLLabelElement> | null | undefined;
}>, never>, never>>, keyof import("react").Component<any, {}, any>>;
export declare const PanelHeaderTitle: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<import("styled-components/dist/types").FastOmit<import("styled-components/dist/types").Substitute<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLSpanElement>, HTMLSpanElement>, Omit<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLSpanElement>, HTMLSpanElement>, "ref"> & {
    ref?: ((instance: HTMLSpanElement | null) => void) | import("react").RefObject<HTMLSpanElement> | null | undefined;
}>, never>, never>>;
export declare const PanelHeaderContent: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, never>>;
export declare const PanelContent: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<import("styled-components/dist/types").FastOmit<import("styled-components/dist/types").Substitute<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, Omit<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, "ref"> & {
    ref?: ((instance: HTMLDivElement | null) => void) | import("react").RefObject<HTMLDivElement> | null | undefined;
}>, never>, never>>;
interface SidePanelSectionProps {
    disabled?: boolean;
}
export declare const SidePanelSection: IStyledComponent<"web", import("styled-components/dist/types").Substitute<import("styled-components/dist/types").FastOmit<import("styled-components/dist/types").Substitute<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, Omit<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, "ref"> & {
    ref?: ((instance: HTMLDivElement | null) => void) | import("react").RefObject<HTMLDivElement> | null | undefined;
}>, never>, SidePanelSectionProps>>;
export declare const SidePanelDivider: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<import("styled-components/dist/types").FastOmit<import("styled-components/dist/types").Substitute<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, Omit<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, "ref"> & {
    ref?: ((instance: HTMLDivElement | null) => void) | import("react").RefObject<HTMLDivElement> | null | undefined;
}>, never>, never>>;
declare type TooltipAttrsProps = {
    interactive?: boolean;
} & TooltipProps & BaseComponentProps;
export declare const Tooltip: IStyledComponent<'web', TooltipAttrsProps>;
export declare type ButtonProps = React.ButtonHTMLAttributes<HTMLButtonElement> & {
    className?: string;
    ref?: React.ForwardedRef<HTMLElement>;
    children?: React.ReactNode;
    negative?: boolean;
    secondary?: boolean;
    link?: boolean;
    floating?: boolean;
    cta?: boolean;
    large?: boolean;
    small?: boolean;
    disabled?: boolean;
    width?: string;
    inactive?: boolean;
    size?: string;
};
export declare const Button: IStyledComponent<"web", import("styled-components/dist/types").Substitute<import("styled-components/dist/types").FastOmit<import("styled-components/dist/types").Substitute<import("react").DetailedHTMLProps<import("react").ButtonHTMLAttributes<HTMLButtonElement>, HTMLButtonElement>, Omit<import("react").DetailedHTMLProps<import("react").ButtonHTMLAttributes<HTMLButtonElement>, HTMLButtonElement>, "ref"> & {
    ref?: ((instance: HTMLButtonElement | null) => void) | import("react").RefObject<HTMLButtonElement> | null | undefined;
}>, never>, ButtonProps>>;
interface InputProps {
    secondary?: boolean;
}
export declare const Input: IStyledComponent<"web", import("styled-components/dist/types").Substitute<import("react").DetailedHTMLProps<import("react").InputHTMLAttributes<HTMLInputElement>, HTMLInputElement>, InputProps>>;
export declare const InputLight: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<import("react").DetailedHTMLProps<import("react").InputHTMLAttributes<HTMLInputElement>, HTMLInputElement>, never>>;
export declare const TextArea: IStyledComponent<"web", import("styled-components/dist/types").Substitute<import("react").DetailedHTMLProps<import("react").TextareaHTMLAttributes<HTMLTextAreaElement>, HTMLTextAreaElement>, InputProps>>;
export declare const TextAreaLight: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<import("react").DetailedHTMLProps<import("react").TextareaHTMLAttributes<HTMLTextAreaElement>, HTMLTextAreaElement>, never>>;
export declare const InlineInput: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<Omit<import("styled-components/dist/types").FastOmit<import("react").DetailedHTMLProps<import("react").InputHTMLAttributes<HTMLInputElement>, HTMLInputElement>, "secondary"> & InputProps, "ref"> & {
    ref?: ((instance: HTMLInputElement | null) => void) | import("react").RefObject<HTMLInputElement> | null | undefined;
}, never>> & Omit<IStyledComponent<"web", import("styled-components/dist/types").Substitute<import("react").DetailedHTMLProps<import("react").InputHTMLAttributes<HTMLInputElement>, HTMLInputElement>, InputProps>>, keyof import("react").Component<any, {}, any>>;
export interface StyledPanelHeaderProps {
    active?: boolean;
    labelRCGColorValues?: RGBColor | null;
    warning?: boolean;
    isValid?: boolean;
}
export declare const StyledPanelHeader: IStyledComponent<"web", import("styled-components/dist/types").Substitute<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, StyledPanelHeaderProps>>;
interface StyledPanelDropdownProps {
    type?: string;
}
export declare const StyledPanelDropdown: IStyledComponent<"web", import("styled-components/dist/types").Substitute<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, StyledPanelDropdownProps>>;
export declare const ButtonGroup: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, never>>;
interface DatasetSquareProps {
    backgroundColor: RGBColor;
}
export declare const DatasetSquare: IStyledComponent<"web", import("styled-components/dist/types").Substitute<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, DatasetSquareProps>>;
interface SelectionButtonProps {
    selected?: boolean;
}
export declare const SelectionButton: IStyledComponent<"web", import("styled-components/dist/types").Substitute<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, SelectionButtonProps>>;
export declare const StyledTable: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<import("react").DetailedHTMLProps<import("react").TableHTMLAttributes<HTMLTableElement>, HTMLTableElement>, never>>;
export declare const StyledModalContent: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, never>>;
export declare const StyledModalVerticalPanel: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<import("styled-components/dist/types").FastOmit<import("styled-components/dist/types").Substitute<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, Omit<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, "ref"> & {
    ref?: ((instance: HTMLDivElement | null) => void) | import("react").RefObject<HTMLDivElement> | null | undefined;
}>, never>, never>>;
export declare const StyledModalSection: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<import("styled-components/dist/types").FastOmit<import("styled-components/dist/types").Substitute<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, Omit<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, "ref"> & {
    ref?: ((instance: HTMLDivElement | null) => void) | import("react").RefObject<HTMLDivElement> | null | undefined;
}>, never>, never>>;
interface StyledModalInputFootnoteProps {
    error?: boolean;
}
export declare const StyledModalInputFootnote: IStyledComponent<"web", import("styled-components/dist/types").Substitute<import("styled-components/dist/types").FastOmit<import("styled-components/dist/types").Substitute<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, Omit<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, "ref"> & {
    ref?: ((instance: HTMLDivElement | null) => void) | import("react").RefObject<HTMLDivElement> | null | undefined;
}>, never>, StyledModalInputFootnoteProps>>;
/**
 * Newer versions of mapbox.gl display an error message banner on top of the map by default
 * which will cause the map to display points in the wrong locations
 * This workaround will hide the error banner.
 */
export declare const StyledMapContainer: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, never>>;
export declare type StyledAttributionProps = {
    mapLibCssClass: string;
    mapLibAttributionCssClass: string;
};
export declare const StyledAttribution: IStyledComponent<"web", import("styled-components/dist/types").Substitute<import("styled-components/dist/types").Substitute<import("styled-components/dist/types").Substitute<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, Omit<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, "ref"> & {
    ref?: ((instance: HTMLDivElement | null) => void) | import("react").RefObject<HTMLDivElement> | null | undefined;
}>, StyledAttributionProps>, StyledAttributionProps>>;
export interface StyledExportSectionProps {
    disabled?: boolean;
}
export declare const StyledExportSection: IStyledComponent<"web", import("styled-components/dist/types").Substitute<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, StyledExportSectionProps>>;
export declare const StyledFilteredOption: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<Omit<import("styled-components/dist/types").FastOmit<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, "selected"> & SelectionButtonProps, "ref"> & {
    ref?: ((instance: HTMLDivElement | null) => void) | import("react").RefObject<HTMLDivElement> | null | undefined;
}, never>> & Omit<IStyledComponent<"web", import("styled-components/dist/types").Substitute<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, SelectionButtonProps>>, keyof import("react").Component<any, {}, any>>;
export declare const StyledType: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<Omit<import("styled-components/dist/types").FastOmit<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, "selected"> & SelectionButtonProps, "ref"> & {
    ref?: ((instance: HTMLDivElement | null) => void) | import("react").RefObject<HTMLDivElement> | null | undefined;
}, never>> & Omit<IStyledComponent<"web", import("styled-components/dist/types").Substitute<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, SelectionButtonProps>>, keyof import("react").Component<any, {}, any>>;
export declare const WidgetContainer: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, never>>;
export declare const BottomWidgetInner: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, never>>;
interface MapControlButtonProps {
    active?: boolean;
}
export declare const MapControlButton: IStyledComponent<"web", import("styled-components/dist/types").Substitute<import("styled-components/dist/types").FastOmit<import("styled-components/dist/types").Substitute<import("styled-components/dist/types").FastOmit<import("styled-components/dist/types").FastOmit<import("styled-components/dist/types").Substitute<import("react").DetailedHTMLProps<import("react").ButtonHTMLAttributes<HTMLButtonElement>, HTMLButtonElement>, Omit<import("react").DetailedHTMLProps<import("react").ButtonHTMLAttributes<HTMLButtonElement>, HTMLButtonElement>, "ref"> & {
    ref?: ((instance: HTMLButtonElement | null) => void) | import("react").RefObject<HTMLButtonElement> | null | undefined;
}>, never>, "link" | "small" | "ref" | "width" | "size" | "secondary" | keyof import("react").ButtonHTMLAttributes<HTMLButtonElement> | "negative" | "floating" | "cta" | "large" | "inactive"> & import("react").ButtonHTMLAttributes<HTMLButtonElement> & {
    className?: string | undefined;
    ref?: import("react").ForwardedRef<HTMLElement> | undefined;
    children?: React.ReactNode;
    negative?: boolean | undefined;
    secondary?: boolean | undefined;
    link?: boolean | undefined;
    floating?: boolean | undefined;
    cta?: boolean | undefined;
    large?: boolean | undefined;
    small?: boolean | undefined;
    disabled?: boolean | undefined;
    width?: string | undefined;
    inactive?: boolean | undefined;
    size?: string | undefined;
}, import("styled-components/dist/types").FastOmit<import("styled-components/dist/types").FastOmit<import("styled-components/dist/types").Substitute<import("react").DetailedHTMLProps<import("react").ButtonHTMLAttributes<HTMLButtonElement>, HTMLButtonElement>, Omit<import("react").DetailedHTMLProps<import("react").ButtonHTMLAttributes<HTMLButtonElement>, HTMLButtonElement>, "ref"> & {
    ref?: ((instance: HTMLButtonElement | null) => void) | import("react").RefObject<HTMLButtonElement> | null | undefined;
}>, never>, "link" | "small" | "ref" | "width" | "size" | "secondary" | keyof import("react").ButtonHTMLAttributes<HTMLButtonElement> | "negative" | "floating" | "cta" | "large" | "inactive"> & import("react").ButtonHTMLAttributes<HTMLButtonElement> & {
    className?: string | undefined;
    ref?: import("react").ForwardedRef<HTMLElement> | undefined;
    children?: React.ReactNode;
    negative?: boolean | undefined;
    secondary?: boolean | undefined;
    link?: boolean | undefined;
    floating?: boolean | undefined;
    cta?: boolean | undefined;
    large?: boolean | undefined;
    small?: boolean | undefined;
    disabled?: boolean | undefined;
    width?: string | undefined;
    inactive?: boolean | undefined;
    size?: string | undefined;
}>, never>, MapControlButtonProps>> & Omit<IStyledComponent<"web", import("styled-components/dist/types").Substitute<import("styled-components/dist/types").FastOmit<import("styled-components/dist/types").Substitute<import("react").DetailedHTMLProps<import("react").ButtonHTMLAttributes<HTMLButtonElement>, HTMLButtonElement>, Omit<import("react").DetailedHTMLProps<import("react").ButtonHTMLAttributes<HTMLButtonElement>, HTMLButtonElement>, "ref"> & {
    ref?: ((instance: HTMLButtonElement | null) => void) | import("react").RefObject<HTMLButtonElement> | null | undefined;
}>, never>, ButtonProps>>, keyof import("react").Component<any, {}, any>>;
export declare const StyledFilterContent: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, never>>;
export declare const TruncatedTitleText: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, never>>;
export declare const CheckMark: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<import("styled-components/dist/types").FastOmit<import("styled-components/dist/types").Substitute<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLSpanElement>, HTMLSpanElement>, Omit<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLSpanElement>, HTMLSpanElement>, "ref"> & {
    ref?: ((instance: HTMLSpanElement | null) => void) | import("react").RefObject<HTMLSpanElement> | null | undefined;
}>, never>, never>>;
export declare const StyledTimePicker: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<{
    amPmAriaLabel?: string | undefined;
    autoFocus?: boolean | undefined;
    className?: import("react-time-picker/dist/cjs/shared/types").ClassName;
    clearAriaLabel?: string | undefined;
    clearIcon?: (import("react").ComponentType<{}> | (string | number | boolean | import("react").ReactElement<any, string | import("react").JSXElementConstructor<any>> | import("prop-types").ReactNodeArray | null) | import("react").ReactElement<any, string | import("react").JSXElementConstructor<any>>) | undefined;
    clockAriaLabel?: string | undefined;
    clockClassName?: import("react-time-picker/dist/cjs/shared/types").ClassName;
    clockIcon?: (import("react").ComponentType<{}> | (string | number | boolean | import("react").ReactElement<any, string | import("react").JSXElementConstructor<any>> | import("prop-types").ReactNodeArray | null) | import("react").ReactElement<any, string | import("react").JSXElementConstructor<any>>) | undefined;
    closeClock?: boolean | undefined;
    'data-testid'?: string | undefined;
    disableClock?: boolean | undefined;
    disabled?: boolean | undefined;
    format?: string | undefined;
    hourAriaLabel?: string | undefined;
    hourPlaceholder?: string | undefined;
    id?: string | undefined;
    isOpen?: boolean | undefined;
    locale?: string | undefined;
    maxDetail?: import("react-time-picker/dist/cjs/shared/types").Detail | undefined;
    maxTime?: string | undefined;
    minTime?: string | undefined;
    minuteAriaLabel?: string | undefined;
    minutePlaceholder?: string | undefined;
    name?: string | undefined;
    nativeInputAriaLabel?: string | undefined;
    onChange?: ((value: import("react-time-picker/dist/cjs/shared/types").Value) => void) | undefined;
    onClockClose?: (() => void) | undefined;
    onClockOpen?: (() => void) | undefined;
    onFocus?: ((event: import("react").FocusEvent<HTMLDivElement, Element>) => void) | undefined;
    onInvalidChange?: (() => void) | undefined;
    openClockOnFocus?: boolean | undefined;
    portalContainer?: HTMLElement | null | undefined;
    required?: boolean | undefined;
    secondAriaLabel?: string | undefined;
    secondPlaceholder?: string | undefined;
    shouldCloseClock?: (({ reason }: {
        reason: import("react-time-picker/dist/cjs/shared/types").CloseReason;
    }) => boolean) | undefined;
    shouldOpenClock?: (({ reason }: {
        reason: import("react-time-picker/dist/cjs/shared/types").OpenReason;
    }) => boolean) | undefined;
    value?: import("react-time-picker/dist/cjs/shared/types").LooseValue | undefined;
} & {
    locale?: string | undefined;
    className?: import("react-clock/dist/cjs/shared/types").ClassName;
    size?: import("csstype").Property.Width<string | number> | undefined;
    formatHour?: typeof import("react-clock/dist/cjs/shared/hourFormatter").formatHour | undefined;
    hourHandLength?: number | undefined;
    hourHandOppositeLength?: number | undefined;
    hourHandWidth?: number | undefined;
    hourMarksLength?: number | undefined;
    hourMarksWidth?: number | undefined;
    minuteHandLength?: number | undefined;
    minuteHandOppositeLength?: number | undefined;
    minuteHandWidth?: number | undefined;
    minuteMarksLength?: number | undefined;
    minuteMarksWidth?: number | undefined;
    renderHourMarks?: boolean | undefined;
    renderMinuteHand?: boolean | undefined;
    renderMinuteMarks?: boolean | undefined;
    renderNumbers?: boolean | undefined;
    renderSecondHand?: boolean | undefined;
    secondHandLength?: number | undefined;
    secondHandOppositeLength?: number | undefined;
    secondHandWidth?: number | undefined;
    useMillisecondPrecision?: boolean | undefined;
} & Omit<{
    onCopy?: ((event: any) => void) | undefined;
    onCut?: ((event: any) => void) | undefined;
    onPaste?: ((event: any) => void) | undefined;
    onCompositionEnd?: ((event: any) => void) | undefined;
    onCompositionStart?: ((event: any) => void) | undefined;
    onCompositionUpdate?: ((event: any) => void) | undefined;
    onFocus?: ((event: any) => void) | undefined;
    onBlur?: ((event: any) => void) | undefined;
    onChange?: ((event: any) => void) | undefined;
    onInput?: ((event: any) => void) | undefined;
    onReset?: ((event: any) => void) | undefined;
    onSubmit?: ((event: any) => void) | undefined;
    onInvalid?: ((event: any) => void) | undefined;
    onLoad?: ((event: any) => void) | undefined;
    onError?: ((event: any) => void) | undefined;
    onKeyDown?: ((event: any) => void) | undefined;
    onKeyPress?: ((event: any) => void) | undefined;
    onKeyUp?: ((event: any) => void) | undefined;
    onAbort?: ((event: any) => void) | undefined;
    onCanPlay?: ((event: any) => void) | undefined;
    onCanPlayThrough?: ((event: any) => void) | undefined;
    onDurationChange?: ((event: any) => void) | undefined;
    onEmptied?: ((event: any) => void) | undefined;
    onEncrypted?: ((event: any) => void) | undefined;
    onEnded?: ((event: any) => void) | undefined;
    onLoadedData?: ((event: any) => void) | undefined;
    onLoadedMetadata?: ((event: any) => void) | undefined;
    onLoadStart?: ((event: any) => void) | undefined;
    onPause?: ((event: any) => void) | undefined;
    onPlay?: ((event: any) => void) | undefined;
    onPlaying?: ((event: any) => void) | undefined;
    onProgress?: ((event: any) => void) | undefined;
    onRateChange?: ((event: any) => void) | undefined;
    onSeeked?: ((event: any) => void) | undefined;
    onSeeking?: ((event: any) => void) | undefined;
    onStalled?: ((event: any) => void) | undefined;
    onSuspend?: ((event: any) => void) | undefined;
    onTimeUpdate?: ((event: any) => void) | undefined;
    onVolumeChange?: ((event: any) => void) | undefined;
    onWaiting?: ((event: any) => void) | undefined;
    onClick?: ((event: any) => void) | undefined;
    onContextMenu?: ((event: any) => void) | undefined;
    onDoubleClick?: ((event: any) => void) | undefined;
    onDrag?: ((event: any) => void) | undefined;
    onDragEnd?: ((event: any) => void) | undefined;
    onDragEnter?: ((event: any) => void) | undefined;
    onDragExit?: ((event: any) => void) | undefined;
    onDragLeave?: ((event: any) => void) | undefined;
    onDragOver?: ((event: any) => void) | undefined;
    onDragStart?: ((event: any) => void) | undefined;
    onDrop?: ((event: any) => void) | undefined;
    onMouseDown?: ((event: any) => void) | undefined;
    onMouseEnter?: ((event: any) => void) | undefined;
    onMouseLeave?: ((event: any) => void) | undefined;
    onMouseMove?: ((event: any) => void) | undefined;
    onMouseOut?: ((event: any) => void) | undefined;
    onMouseOver?: ((event: any) => void) | undefined;
    onMouseUp?: ((event: any) => void) | undefined;
    onSelect?: ((event: any) => void) | undefined;
    onTouchCancel?: ((event: any) => void) | undefined;
    onTouchEnd?: ((event: any) => void) | undefined;
    onTouchMove?: ((event: any) => void) | undefined;
    onTouchStart?: ((event: any) => void) | undefined;
    onPointerDown?: ((event: any) => void) | undefined;
    onPointerMove?: ((event: any) => void) | undefined;
    onPointerUp?: ((event: any) => void) | undefined;
    onPointerCancel?: ((event: any) => void) | undefined;
    onPointerEnter?: ((event: any) => void) | undefined;
    onPointerLeave?: ((event: any) => void) | undefined;
    onPointerOver?: ((event: any) => void) | undefined;
    onPointerOut?: ((event: any) => void) | undefined;
    onGotPointerCapture?: ((event: any) => void) | undefined;
    onLostPointerCapture?: ((event: any) => void) | undefined;
    onScroll?: ((event: any) => void) | undefined;
    onWheel?: ((event: any) => void) | undefined;
    onAnimationStart?: ((event: any) => void) | undefined;
    onAnimationEnd?: ((event: any) => void) | undefined;
    onAnimationIteration?: ((event: any) => void) | undefined;
    onTransitionEnd?: ((event: any) => void) | undefined;
    onToggle?: ((event: any) => void) | undefined;
}, "onFocus" | "onChange">, never>> & Omit<import("react").FC<import("react-time-picker").TimePickerProps>, keyof import("react").Component<any, {}, any>>;
export declare const StyledDatePicker: IStyledComponent<"web", import("styled-components/dist/types").FastOmit<{
    autoFocus?: boolean | undefined;
    calendarAriaLabel?: string | undefined;
    calendarClassName?: import("react-date-picker/dist/cjs/shared/types").ClassName;
    calendarIcon?: (import("react").ComponentType<{}> | (string | number | boolean | import("prop-types").ReactNodeArray | import("react").ReactElement<any, string | import("react").JSXElementConstructor<any>> | null) | import("react").ReactElement<any, string | import("react").JSXElementConstructor<any>>) | undefined;
    className?: import("react-date-picker/dist/cjs/shared/types").ClassName;
    clearAriaLabel?: string | undefined;
    clearIcon?: (import("react").ComponentType<{}> | (string | number | boolean | import("prop-types").ReactNodeArray | import("react").ReactElement<any, string | import("react").JSXElementConstructor<any>> | null) | import("react").ReactElement<any, string | import("react").JSXElementConstructor<any>>) | undefined;
    closeCalendar?: boolean | undefined;
    'data-testid'?: string | undefined;
    dayAriaLabel?: string | undefined;
    dayPlaceholder?: string | undefined;
    disableCalendar?: boolean | undefined;
    disabled?: boolean | undefined;
    format?: string | undefined;
    id?: string | undefined;
    isOpen?: boolean | undefined;
    locale?: string | undefined;
    maxDate?: Date | undefined;
    maxDetail?: import("react-date-picker/dist/cjs/shared/types").Detail | undefined;
    minDate?: Date | undefined;
    monthAriaLabel?: string | undefined;
    monthPlaceholder?: string | undefined;
    name?: string | undefined;
    nativeInputAriaLabel?: string | undefined;
    onCalendarClose?: (() => void) | undefined;
    onCalendarOpen?: (() => void) | undefined;
    onChange?: ((value: import("react-date-picker/dist/cjs/shared/types").Value) => void) | undefined;
    onFocus?: ((event: import("react").FocusEvent<HTMLDivElement, Element>) => void) | undefined;
    onInvalidChange?: (() => void) | undefined;
    openCalendarOnFocus?: boolean | undefined;
    portalContainer?: HTMLElement | null | undefined;
    required?: boolean | undefined;
    returnValue?: "range" | "end" | "start" | undefined;
    shouldCloseCalendar?: ((props: {
        reason: import("react-date-picker/dist/cjs/shared/types").CloseReason;
    }) => boolean) | undefined;
    shouldOpenCalendar?: ((props: {
        reason: import("react-date-picker/dist/cjs/shared/types").OpenReason;
    }) => boolean) | undefined;
    showLeadingZeros?: boolean | undefined;
    value?: import("react-date-picker/dist/cjs/shared/types").LooseValue | undefined;
    yearAriaLabel?: string | undefined;
    yearPlaceholder?: string | undefined;
} & {
    value?: import("react-calendar/dist/cjs/shared/types").LooseValue | undefined;
    view?: import("react-calendar/dist/cjs/shared/types").View | undefined;
    locale?: string | undefined;
    key?: import("react").Key | null | undefined;
    defaultValue?: import("react-calendar/dist/cjs/shared/types").LooseValue | undefined;
    activeStartDate?: Date | undefined;
    allowPartialRange?: boolean | undefined;
    calendarType?: import("react-calendar/dist/cjs/shared/types").CalendarType | import("react-calendar/dist/cjs/shared/types").DeprecatedCalendarType | undefined;
    defaultActiveStartDate?: Date | undefined;
    defaultView?: import("react-calendar/dist/cjs/shared/types").View | undefined;
    formatDay?: ((locale: string | undefined, date: Date) => string) | undefined;
    formatLongDate?: ((locale: string | undefined, date: Date) => string) | undefined;
    formatMonth?: ((locale: string | undefined, date: Date) => string) | undefined;
    formatMonthYear?: ((locale: string | undefined, date: Date) => string) | undefined;
    formatShortWeekday?: ((locale: string | undefined, date: Date) => string) | undefined;
    formatWeekday?: ((locale: string | undefined, date: Date) => string) | undefined;
    formatYear?: ((locale: string | undefined, date: Date) => string) | undefined;
    goToRangeStartOnSelect?: boolean | undefined;
    inputRef?: import("react").Ref<HTMLDivElement> | undefined;
    maxDate?: Date | undefined;
    minDate?: Date | undefined;
    minDetail?: import("react-calendar/dist/cjs/shared/types").Detail | undefined;
    navigationAriaLabel?: string | undefined;
    navigationAriaLive?: "off" | "assertive" | "polite" | undefined;
    navigationLabel?: import("react-calendar").NavigationLabelFunc | undefined;
    next2AriaLabel?: string | undefined;
    next2Label?: import("react").ReactNode;
    nextAriaLabel?: string | undefined;
    nextLabel?: import("react").ReactNode;
    onActiveStartDateChange?: (({ action, activeStartDate, value, view }: import("react-calendar").OnArgs) => void) | undefined;
    onClickDay?: import("react-calendar").OnClickFunc | undefined;
    onClickDecade?: import("react-calendar").OnClickFunc | undefined;
    onClickMonth?: import("react-calendar").OnClickFunc | undefined;
    onClickWeekNumber?: import("react-calendar").OnClickWeekNumberFunc | undefined;
    onClickYear?: import("react-calendar").OnClickFunc | undefined;
    onDrillDown?: (({ action, activeStartDate, value, view }: import("react-calendar").OnArgs) => void) | undefined;
    onDrillUp?: (({ action, activeStartDate, value, view }: import("react-calendar").OnArgs) => void) | undefined;
    onViewChange?: (({ action, activeStartDate, value, view }: import("react-calendar").OnArgs) => void) | undefined;
    prev2AriaLabel?: string | undefined;
    prev2Label?: import("react").ReactNode;
    prevAriaLabel?: string | undefined;
    prevLabel?: import("react").ReactNode;
    returnValue?: "range" | "end" | "start" | undefined;
    selectRange?: boolean | undefined;
    showDoubleView?: boolean | undefined;
    showFixedNumberOfWeeks?: boolean | undefined;
    showNavigation?: boolean | undefined;
    showNeighboringCentury?: boolean | undefined;
    showNeighboringDecade?: boolean | undefined;
    showNeighboringMonth?: boolean | undefined;
    showWeekNumbers?: boolean | undefined;
    tileClassName?: import("react-calendar").TileClassNameFunc | import("react-calendar/dist/cjs/shared/types").ClassName;
    tileContent?: import("react").ReactNode | import("react-calendar").TileContentFunc;
    tileDisabled?: import("react-calendar").TileDisabledFunc | undefined;
} & Omit<{
    onCopy?: ((event: any) => void) | undefined;
    onCut?: ((event: any) => void) | undefined;
    onPaste?: ((event: any) => void) | undefined;
    onCompositionEnd?: ((event: any) => void) | undefined;
    onCompositionStart?: ((event: any) => void) | undefined;
    onCompositionUpdate?: ((event: any) => void) | undefined;
    onFocus?: ((event: any) => void) | undefined;
    onBlur?: ((event: any) => void) | undefined;
    onChange?: ((event: any) => void) | undefined;
    onInput?: ((event: any) => void) | undefined;
    onReset?: ((event: any) => void) | undefined;
    onSubmit?: ((event: any) => void) | undefined;
    onInvalid?: ((event: any) => void) | undefined;
    onLoad?: ((event: any) => void) | undefined;
    onError?: ((event: any) => void) | undefined;
    onKeyDown?: ((event: any) => void) | undefined;
    onKeyPress?: ((event: any) => void) | undefined;
    onKeyUp?: ((event: any) => void) | undefined;
    onAbort?: ((event: any) => void) | undefined;
    onCanPlay?: ((event: any) => void) | undefined;
    onCanPlayThrough?: ((event: any) => void) | undefined;
    onDurationChange?: ((event: any) => void) | undefined;
    onEmptied?: ((event: any) => void) | undefined;
    onEncrypted?: ((event: any) => void) | undefined;
    onEnded?: ((event: any) => void) | undefined;
    onLoadedData?: ((event: any) => void) | undefined;
    onLoadedMetadata?: ((event: any) => void) | undefined;
    onLoadStart?: ((event: any) => void) | undefined;
    onPause?: ((event: any) => void) | undefined;
    onPlay?: ((event: any) => void) | undefined;
    onPlaying?: ((event: any) => void) | undefined;
    onProgress?: ((event: any) => void) | undefined;
    onRateChange?: ((event: any) => void) | undefined;
    onSeeked?: ((event: any) => void) | undefined;
    onSeeking?: ((event: any) => void) | undefined;
    onStalled?: ((event: any) => void) | undefined;
    onSuspend?: ((event: any) => void) | undefined;
    onTimeUpdate?: ((event: any) => void) | undefined;
    onVolumeChange?: ((event: any) => void) | undefined;
    onWaiting?: ((event: any) => void) | undefined;
    onClick?: ((event: any) => void) | undefined;
    onContextMenu?: ((event: any) => void) | undefined;
    onDoubleClick?: ((event: any) => void) | undefined;
    onDrag?: ((event: any) => void) | undefined;
    onDragEnd?: ((event: any) => void) | undefined;
    onDragEnter?: ((event: any) => void) | undefined;
    onDragExit?: ((event: any) => void) | undefined;
    onDragLeave?: ((event: any) => void) | undefined;
    onDragOver?: ((event: any) => void) | undefined;
    onDragStart?: ((event: any) => void) | undefined;
    onDrop?: ((event: any) => void) | undefined;
    onMouseDown?: ((event: any) => void) | undefined;
    onMouseEnter?: ((event: any) => void) | undefined;
    onMouseLeave?: ((event: any) => void) | undefined;
    onMouseMove?: ((event: any) => void) | undefined;
    onMouseOut?: ((event: any) => void) | undefined;
    onMouseOver?: ((event: any) => void) | undefined;
    onMouseUp?: ((event: any) => void) | undefined;
    onSelect?: ((event: any) => void) | undefined;
    onTouchCancel?: ((event: any) => void) | undefined;
    onTouchEnd?: ((event: any) => void) | undefined;
    onTouchMove?: ((event: any) => void) | undefined;
    onTouchStart?: ((event: any) => void) | undefined;
    onPointerDown?: ((event: any) => void) | undefined;
    onPointerMove?: ((event: any) => void) | undefined;
    onPointerUp?: ((event: any) => void) | undefined;
    onPointerCancel?: ((event: any) => void) | undefined;
    onPointerEnter?: ((event: any) => void) | undefined;
    onPointerLeave?: ((event: any) => void) | undefined;
    onPointerOver?: ((event: any) => void) | undefined;
    onPointerOut?: ((event: any) => void) | undefined;
    onGotPointerCapture?: ((event: any) => void) | undefined;
    onLostPointerCapture?: ((event: any) => void) | undefined;
    onScroll?: ((event: any) => void) | undefined;
    onWheel?: ((event: any) => void) | undefined;
    onAnimationStart?: ((event: any) => void) | undefined;
    onAnimationEnd?: ((event: any) => void) | undefined;
    onAnimationIteration?: ((event: any) => void) | undefined;
    onTransitionEnd?: ((event: any) => void) | undefined;
    onToggle?: ((event: any) => void) | undefined;
}, "onFocus" | "onChange">, never>> & Omit<import("react").FC<import("react-date-picker").DatePickerProps>, keyof import("react").Component<any, {}, any>>;
export {};
