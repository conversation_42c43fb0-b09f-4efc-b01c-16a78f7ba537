"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.isPMTilesUrl = void 0;
exports.parseUri = parseUri;
exports.validateUrl = validateUrl;
// SPDX-License-Identifier: MIT
// Copyright contributors to the kepler.gl project

/**
 * Allows to break down a url into multiple params
 * from http://blog.stevenlevithan.com/archives/parseuri
 */
function parseUri(str) {
  var o = parseUri.options;
  var m = o.parser[o.strictMode ? 'strict' : 'loose'].exec(str);
  var uri = {};
  var i = 14;
  while (i--) uri[o.key[i]] = (m === null || m === void 0 ? void 0 : m[i]) || '';
  uri[o.q.name] = {};
  uri[o.key[12]].replace(o.q.parser, function ($0, $1, $2) {
    if ($1) uri[o.q.name][$1] = $2;
  });
  return uri;
}
parseUri.options = {
  strictMode: false,
  key: ['source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'],
  q: {
    name: 'queryKey',
    parser: /(?:^|&)([^&=]*)=?([^&]*)/g
  },
  parser: {
    strict:
    // eslint-disable-next-line no-useless-escape
    /^(?:([^:\/?#]+):)?(?:\/\/((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\/?#]*)(?::(\d*))?))?((((?:[^?#\/]*\/)*)([^?#]*))(?:\?([^#]*))?(?:#(.*))?)/,
    loose:
    // eslint-disable-next-line no-useless-escape
    /^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/
  }
};

/**
 * Validates an url
 * @param str
 */
function validateUrl(str) {
  try {
    new URL(str);
    return true;
  } catch (_unused) {
    return false;
  }
}

/**
 * Checks whether a given URL points to a PMTiles file.
 * @param url The URL to check.
 * @returns True if the URL includes '.pmtiles', otherwise false.
 */
var isPMTilesUrl = exports.isPMTilesUrl = function isPMTilesUrl(url) {
  return url === null || url === void 0 ? void 0 : url.includes('.pmtiles');
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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