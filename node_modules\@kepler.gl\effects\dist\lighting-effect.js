"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var _toConsumableArray2 = _interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _get2 = _interopRequireDefault(require("@babel/runtime/helpers/get"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var _core = require("@deck.gl/core");
var _momentTimezone = _interopRequireDefault(require("moment-timezone"));
var _constants = require("@kepler.gl/constants");
var _utils = require("@kepler.gl/utils");
var _effect = _interopRequireDefault(require("./effect"));
var _customDeckLightingEffect = _interopRequireDefault(require("./custom-deck-lighting-effect"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2["default"])(o), (0, _possibleConstructorReturn2["default"])(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2["default"])(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _superPropGet(t, e, r, o) { var p = (0, _get2["default"])((0, _getPrototypeOf2["default"])(1 & o ? t.prototype : t), e, r); return 2 & o ? function (t) { return p.apply(r, t); } : p; }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2["default"])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; } // SPDX-License-Identifier: MIT
// Copyright contributors to the kepler.gl project
var LIGHT_AND_SHADOW_EFFECT_DESC = _objectSpread(_objectSpread({}, _constants.LIGHT_AND_SHADOW_EFFECT), {}, {
  "class": null
});
var LightingEffect = /*#__PURE__*/function (_Effect) {
  // deckEffect: PostProcessEffect | LightingEffect | null;

  function LightingEffect(props) {
    (0, _classCallCheck2["default"])(this, LightingEffect);
    return _callSuper(this, LightingEffect, [props]);
  }
  (0, _inherits2["default"])(LightingEffect, _Effect);
  return (0, _createClass2["default"])(LightingEffect, [{
    key: "_initializeEffect",
    value: function _initializeEffect() {
      this.parameters = _objectSpread(_objectSpread({}, _constants.DEFAULT_LIGHT_AND_SHADOW_PROPS), {}, {
        timezone: _momentTimezone["default"].tz.guess(true)
      }, this.parameters);
      var parameters = this.parameters;
      var ambientLight = new _core.AmbientLight({
        color: parameters.ambientLightColor,
        intensity: parameters.ambientLightIntensity
      });
      var sunLight = new _core._SunLight({
        timestamp: parameters.timestamp,
        color: parameters.sunLightColor,
        intensity: parameters.sunLightIntensity,
        _shadow: true
      });
      this.deckEffect = new _customDeckLightingEffect["default"]({
        ambientLight: ambientLight,
        sunLight: sunLight
      });
      if (this.deckEffect) {
        this.deckEffect.shadowColor = [].concat((0, _toConsumableArray2["default"])((0, _utils.normalizeColor)(parameters.shadowColor)), [parameters.shadowIntensity]);
      }
    }
  }, {
    key: "getDefaultProps",
    value: function getDefaultProps() {
      var props = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
      return _superPropGet(LightingEffect, "getDefaultProps", this, 3)([_objectSpread({
        type: LIGHT_AND_SHADOW_EFFECT_DESC.type
      }, props)]);
    }
  }, {
    key: "setProps",
    value: function setProps(props) {
      _superPropGet(LightingEffect, "setProps", this, 3)([props]);

      // any uniform updated?
      if (props.parameters) {
        var parameters = this.parameters;
        if (this.type === LIGHT_AND_SHADOW_EFFECT_DESC.type) {
          /** @type {LightingEffect} */
          var effect = this.deckEffect;
          if (effect) {
            effect.shadowColor = [].concat((0, _toConsumableArray2["default"])((0, _utils.normalizeColor)(parameters.shadowColor)), [parameters.shadowIntensity]);
            effect.ambientLight.intensity = parameters.ambientLightIntensity;
            effect.ambientLight.color = parameters.ambientLightColor.slice();
            var sunLight = effect.directionalLights[0];
            if (sunLight) {
              sunLight.intensity = parameters.sunLightIntensity;
              sunLight.color = parameters.sunLightColor.slice();
              sunLight.timestamp = parameters.timestamp;
            }
          }
        }
      }
    }
  }]);
}(_effect["default"]);
var _default = exports["default"] = LightingEffect;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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