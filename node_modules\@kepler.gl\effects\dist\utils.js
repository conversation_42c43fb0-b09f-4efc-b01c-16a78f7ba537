"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.createEffect = createEffect;
var _constants = require("@kepler.gl/constants");
var _lightingEffect = _interopRequireDefault(require("./lighting-effect"));
var _postProcessingEffect = _interopRequireDefault(require("./post-processing-effect"));
// SPDX-License-Identifier: MIT
// Copyright contributors to the kepler.gl project

function createEffect(params) {
  if ((params === null || params === void 0 ? void 0 : params.type) === _constants.LIGHT_AND_SHADOW_EFFECT.type) {
    return new _lightingEffect["default"](params);
  }
  return new _postProcessingEffect["default"](params);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfY29uc3RhbnRzIiwicmVxdWlyZSIsIl9saWdodGluZ0VmZmVjdCIsIl9pbnRlcm9wUmVxdWlyZURlZmF1bHQiLCJfcG9zdFByb2Nlc3NpbmdFZmZlY3QiLCJjcmVhdGVFZmZlY3QiLCJwYXJhbXMiLCJ0eXBlIiwiTElHSFRfQU5EX1NIQURPV19FRkZFQ1QiLCJMaWdodGluZ0VmZmVjdCIsIlBvc3RQcm9jZXNzRWZmZWN0Il0sInNvdXJjZXMiOlsiLi4vc3JjL3V0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFNQRFgtTGljZW5zZS1JZGVudGlmaWVyOiBNSVRcbi8vIENvcHlyaWdodCBjb250cmlidXRvcnMgdG8gdGhlIGtlcGxlci5nbCBwcm9qZWN0XG5cbmltcG9ydCB7RWZmZWN0IGFzIEVmZmVjdEludGVyZmFjZSwgRWZmZWN0UHJvcHNQYXJ0aWFsfSBmcm9tICdAa2VwbGVyLmdsL3R5cGVzJztcbmltcG9ydCB7TElHSFRfQU5EX1NIQURPV19FRkZFQ1R9IGZyb20gJ0BrZXBsZXIuZ2wvY29uc3RhbnRzJztcblxuaW1wb3J0IExpZ2h0aW5nRWZmZWN0IGZyb20gJy4vbGlnaHRpbmctZWZmZWN0JztcbmltcG9ydCBQb3N0UHJvY2Vzc0VmZmVjdCBmcm9tICcuL3Bvc3QtcHJvY2Vzc2luZy1lZmZlY3QnO1xuXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlRWZmZWN0KHBhcmFtczogRWZmZWN0UHJvcHNQYXJ0aWFsKTogRWZmZWN0SW50ZXJmYWNlIHtcbiAgaWYgKHBhcmFtcz8udHlwZSA9PT0gTElHSFRfQU5EX1NIQURPV19FRkZFQ1QudHlwZSkge1xuICAgIHJldHVybiBuZXcgTGlnaHRpbmdFZmZlY3QocGFyYW1zKTtcbiAgfVxuICByZXR1cm4gbmV3IFBvc3RQcm9jZXNzRWZmZWN0KHBhcmFtcyk7XG59XG4iXSwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFJQSxJQUFBQSxVQUFBLEdBQUFDLE9BQUE7QUFFQSxJQUFBQyxlQUFBLEdBQUFDLHNCQUFBLENBQUFGLE9BQUE7QUFDQSxJQUFBRyxxQkFBQSxHQUFBRCxzQkFBQSxDQUFBRixPQUFBO0FBUEE7QUFDQTs7QUFRTyxTQUFTSSxZQUFZQSxDQUFDQyxNQUEwQixFQUFtQjtFQUN4RSxJQUFJLENBQUFBLE1BQU0sYUFBTkEsTUFBTSx1QkFBTkEsTUFBTSxDQUFFQyxJQUFJLE1BQUtDLGtDQUF1QixDQUFDRCxJQUFJLEVBQUU7SUFDakQsT0FBTyxJQUFJRSwwQkFBYyxDQUFDSCxNQUFNLENBQUM7RUFDbkM7RUFDQSxPQUFPLElBQUlJLGdDQUFpQixDQUFDSixNQUFNLENBQUM7QUFDdEMiLCJpZ25vcmVMaXN0IjpbXX0=