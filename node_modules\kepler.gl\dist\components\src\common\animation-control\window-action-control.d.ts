import React from 'react';
declare function WindowActionControlFactory(): ({ toggleAnimationWindowControl, showAnimationWindowControl, btnStyle, animationItems, animationWindow, buttonHeight, setFilterAnimationWindow }: {
    toggleAnimationWindowControl: any;
    showAnimationWindowControl: any;
    btnStyle: any;
    animationItems: any;
    animationWindow: any;
    buttonHeight: any;
    setFilterAnimationWindow: any;
}) => React.JSX.Element | null;
export default WindowActionControlFactory;
