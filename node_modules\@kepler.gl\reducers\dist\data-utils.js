"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.findMapBounds = findMapBounds;
exports.processLayerBounds = processLayerBounds;
var _utils = require("@kepler.gl/utils");
// SPDX-License-Identifier: MIT
// Copyright contributors to the kepler.gl project

/**
 * takes a list of layer bounds and returns a single bound
 */
function processLayerBounds(layerBounds) {
  return layerBounds.reduce(function (res, b) {
    var minLongitude = Math.min(res[0], b[0]);
    var minLatitude = Math.min(res[1], b[1]);
    var maxLongitude = Math.max(res[2], b[2]);
    var maxLatitude = Math.max(res[3], b[3]);

    // for some reason WebMercatorViewport can't handle latitude -90,90 and throws an error
    // so we default to lat/lng (0,0)
    // viewport.js:81 Uncaught Error: Pixel project matrix not invertible
    // at WebMercatorViewport16.Viewport5 (viewport.js:81:13)
    // at new WebMercatorViewport16 (web-mercator-viewport.js:92:5)
    // at getViewportFromMapState (map-utils.js:46:66)
    return [(0, _utils.validateLongitude)(minLongitude), (0, _utils.validateLatitude)(minLatitude), (0, _utils.validateLongitude)(maxLongitude), (0, _utils.validateLatitude)(maxLatitude)];
  }, [_utils.MAX_LONGITUDE, _utils.MAX_LATITUDE, _utils.MIN_LONGITUDE, _utils.MIN_LATITUDE]);
}

/**
 * return center of map from given points
 * @param layers
 * @returns coordinates of map center, empty if not found
 */
function findMapBounds(layers) {
  // find bounds in formatted layerData
  // take ALL layers into account when finding map bounds
  var availableLayerBounds = layers.reduce(function (res, l) {
    if (l.meta && l.meta.bounds) {
      res.push(l.meta.bounds);
    }
    return res;
  }, []);
  // return null if no layer is available
  if (availableLayerBounds.length === 0) {
    return null;
  }
  // merge bounds in each layer
  return processLayerBounds(availableLayerBounds);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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