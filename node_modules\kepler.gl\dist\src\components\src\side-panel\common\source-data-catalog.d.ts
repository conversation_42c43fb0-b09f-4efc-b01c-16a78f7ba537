import React from 'react';
import { openDeleteModal, VisStateActions, ActionHandler } from '@kepler.gl/actions';
import { DataContainerInterface } from '@kepler.gl/utils';
import { RGBColor } from '@kepler.gl/types';
import DatasetTitleFactory from './dataset-title';
import DatasetInfoFactory from './dataset-info';
declare type MiniDataset = {
    id: string;
    color: RGBColor;
    label?: string;
    dataContainer: DataContainerInterface;
    type?: string;
};
declare type MiniDatasets = {
    [key: string]: MiniDataset;
};
export declare type SourceDataCatalogProps = {
    datasets: MiniDatasets;
    showDeleteDataset?: boolean;
    onTitleClick?: () => void;
    showDatasetTable?: ActionHandler<typeof VisStateActions.showDatasetTable>;
    updateTableColor: ActionHandler<typeof VisStateActions.updateTableColor>;
    removeDataset?: ActionHandler<typeof openDeleteModal>;
};
declare function SourceDataCatalogFactory(DatasetTitle: ReturnType<typeof DatasetTitleFactory>, DatasetInfo: ReturnType<typeof DatasetInfoFactory>): React.FC<SourceDataCatalogProps>;
declare namespace SourceDataCatalogFactory {
    var deps: (typeof DatasetInfoFactory | typeof DatasetTitleFactory)[];
}
export default SourceDataCatalogFactory;
