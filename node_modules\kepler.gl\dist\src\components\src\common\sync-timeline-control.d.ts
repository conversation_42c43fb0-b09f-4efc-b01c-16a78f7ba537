import React from 'react';
export declare type SyncTimelineAnimationItem = {
    id: string;
    content: React.ElementType;
    tooltip: string;
};
export declare type SyncTimelineControlProps = {
    syncTimelineMode: string;
    setFilterSyncTimelineMode: (id: string) => void;
    syncTimelineAnimationItems: SyncTimelineAnimationItem[];
    btnStyle: Record<string, any>;
};
export declare const SYNC_TIMELINE_ANIMATION_ITEMS: Record<string, {
    id: number;
    content: React.ElementType;
    tooltip: string;
}>;
declare function SyncTimelineControlFactory(): ({ syncTimelineAnimationItems, syncTimelineMode, btnStyle, setFilterSyncTimelineMode }: {
    syncTimelineAnimationItems?: Record<string, {
        id: number;
        content: React.ElementType<any, keyof React.JSX.IntrinsicElements>;
        tooltip: string;
    }> | undefined;
    syncTimelineMode: any;
    btnStyle: any;
    setFilterSyncTimelineMode: any;
}) => React.JSX.Element;
export default SyncTimelineControlFactory;
