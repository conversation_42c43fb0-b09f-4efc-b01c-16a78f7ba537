import { ExportFileOptions, ExportFileToCloudPayload, OnErrorCallBack, OnSuccessCallBack } from '@kepler.gl/types';
import { Provider } from '@kepler.gl/cloud-providers';
export declare const ActionTypes: {
    EXPORT_FILE_TO_CLOUD: "@@kepler.gl/EXPORT_FILE_TO_CLOUD";
    EXPORT_FILE_SUCCESS: "@@kepler.gl/EXPORT_FILE_SUCCESS";
    EXPORT_FILE_ERROR: "@@kepler.gl/EXPORT_FILE_ERROR";
    RESET_PROVIDER_STATUS: "@@kepler.gl/RESET_PROVIDER_STATUS";
    POST_SAVE_LOAD_SUCCESS: "@@kepler.gl/POST_SAVE_LOAD_SUCCESS";
    LOAD_CLOUD_MAP: "@@kepler.gl/LOAD_CLOUD_MAP";
    LOAD_CLOUD_MAP_SUCCESS: "@@kepler.gl/LOAD_CLOUD_MAP_SUCCESS";
    LOAD_CLOUD_MAP_ERROR: "@@kepler.gl/LOAD_CLOUD_MAP_ERROR";
};
/**
 * Call provider to upload file to cloud
 * @param mapData
 * @param provider
 * @param options
 * @param onSuccess
 * @param onError
 * @param closeModal
 */
export declare const exportFileToCloud: (p: ExportFileToCloudPayload) => {
    type: typeof ActionTypes.EXPORT_FILE_TO_CLOUD;
    payload: ExportFileToCloudPayload;
};
/** EXPORT_FILE_SUCCESS */
export declare type ExportFileSuccessPayload = {
    response: any;
    provider: Provider;
    options?: ExportFileOptions;
    onSuccess?: OnSuccessCallBack;
    closeModal?: boolean;
};
export declare const exportFileSuccess: (p: ExportFileSuccessPayload) => {
    type: typeof ActionTypes.EXPORT_FILE_SUCCESS;
    payload: ExportFileSuccessPayload;
};
/** EXPORT_FILE_ERROR */
export declare type ExportFileErrorPayload = {
    error: any;
    provider: Provider;
    options?: ExportFileOptions;
    onError?: OnErrorCallBack;
};
export declare const exportFileError: (p: ExportFileErrorPayload) => {
    type: typeof ActionTypes.EXPORT_FILE_ERROR;
    payload: ExportFileErrorPayload;
};
/** POST_SAVE_LOAD_SUCCESS */
export declare type PostSaveLoadSuccessPayload = string;
export declare const postSaveLoadSuccess: (p: PostSaveLoadSuccessPayload) => {
    type: typeof ActionTypes.POST_SAVE_LOAD_SUCCESS;
    payload: PostSaveLoadSuccessPayload;
};
export declare const resetProviderStatus: () => {
    type: typeof ActionTypes.RESET_PROVIDER_STATUS;
};
/** LOAD_CLOUD_MAP */
export declare type LoadCloudMapPayload = {
    loadParams: any;
    provider: string;
    onSuccess?: any;
    onError?: OnErrorCallBack;
};
export declare const loadCloudMap: (p: LoadCloudMapPayload) => {
    type: typeof ActionTypes.LOAD_CLOUD_MAP;
    payload: LoadCloudMapPayload;
};
/** LOAD_CLOUD_MAP_SUCCESS */
declare type LoadCloudMapSuccessCallback = (p: {
    response: any;
    loadParams: any;
    provider: Provider;
}) => any;
export declare type LoadCloudMapSuccessPayload = {
    response: any;
    loadParams: any;
    provider: Provider;
    onSuccess?: LoadCloudMapSuccessCallback;
    onError?: OnErrorCallBack;
};
export declare const loadCloudMapSuccess: (p: LoadCloudMapSuccessPayload) => {
    type: typeof ActionTypes.LOAD_CLOUD_MAP_SUCCESS;
    payload: LoadCloudMapSuccessPayload;
};
/** LOAD_CLOUD_MAP_ERROR */
export declare type LoadCloudMapErrorPayload = {
    error: any;
    provider: Provider;
    onError?: OnErrorCallBack;
};
export declare const loadCloudMapError: (p: LoadCloudMapErrorPayload) => {
    type: typeof ActionTypes.LOAD_CLOUD_MAP_ERROR;
    payload: LoadCloudMapErrorPayload;
};
export {};
