{"name": "@math.gl/core", "description": "Array-based 3D Math Classes optimized for WebGL applications", "license": "MIT", "type": "module", "publishConfig": {"access": "public"}, "version": "4.1.0", "keywords": ["webgl", "javascript", "math", "matrix", "matrix4", "vector", "vector2", "vector3", "vector4", "quaternion", "euler", "spherical", "coordinates", "3d"], "repository": {"type": "git", "url": "https://github.com/visgl/math.gl.git"}, "types": "dist/index.d.ts", "main": "dist/index.cjs", "module": "dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "files": ["dist", "src"], "sideEffects": ["./src/lib/common.js"], "dependencies": {"@math.gl/types": "4.1.0"}, "gitHead": "76820f54e2a9034b42bd24ffeb381cc4e01ad46e"}