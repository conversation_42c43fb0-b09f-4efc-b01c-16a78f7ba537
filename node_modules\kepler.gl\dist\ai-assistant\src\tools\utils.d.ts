import { Layer } from '@kepler.gl/layers';
import { Datasets, KeplerTable } from '@kepler.gl/table';
export declare function checkDatasetNotExists(datasets: Datasets, datasetName: string, functionName: string): {
    name: string;
    result: {
        success: boolean;
        details: string;
    };
} | null;
export declare function checkFieldNotExists(dataset: KeplerTable, fieldName: string, functionName: string): {
    type: string;
    name: string;
    result: {
        success: boolean;
        details: string;
    };
} | null;
export declare function interpolateColor(originalColors: string[], numberOfColors: number): string[];
export declare function getValuesFromDataset(datasets: Datasets, datasetName: string, variableName: string): number[];
export declare function highlightRows(datasets: Datasets, layers: Layer[], datasetName: string, selectedRowIndices: number[], layerSetIsValid: (layer: Layer, isValid: boolean) => void): void;
export declare function getDatasetContext(datasets: Datasets, layers: Layer[]): string;
