import { default as ActionTypes } from './action-types';
import { Merge } from '@kepler.gl/types';
import { ExportImage } from '@kepler.gl/constants';
/** TOGGLE_SIDE_PANEL */
export declare type ToggleSidePanelUpdaterAction = {
    payload: string | null;
};
/**
 * Toggle active side panel
 * @memberof uiStateActions
 * @param id  id of side panel to be shown, one of `layer`, `filter`, `interaction`, `map`
 * @public
 */
export declare const toggleSidePanel: (id: string | null) => Merge<ToggleSidePanelUpdaterAction, {
    type: typeof ActionTypes.TOGGLE_SIDE_PANEL;
}>;
/** TOGGLE_MODAL */
export declare type ToggleModalUpdaterAction = {
    payload: string | null;
};
/**
 * Show and hide modal dialog
 * @memberof uiStateActions
 * @param id - id of modal to be shown, null to hide modals. One of:
 *  - [`DATA_TABLE_ID`](../constants/default-settings.md#data_table_id)
 *  - [`DELETE_DATA_ID`](../constants/default-settings.md#delete_data_id)
 *  - [`ADD_DATA_ID`](../constants/default-settings.md#add_data_id)
 *  - [`EXPORT_IMAGE_ID`](../constants/default-settings.md#export_image_id)
 *  - [`EXPORT_DATA_ID`](../constants/default-settings.md#export_data_id)
 *  - [`ADD_MAP_STYLE_ID`](../constants/default-settings.md#add_map_style_id)
 * @public
 */
export declare const toggleModal: (id: ToggleModalUpdaterAction['payload']) => Merge<ToggleModalUpdaterAction, {
    type: typeof ActionTypes.TOGGLE_MODAL;
}>;
/** SHOW_EXPORT_DROPDOWN */
export declare type ShowExportDropdownUpdaterAction = {
    payload: string;
};
/**
 * Hide and show side panel header dropdown, activated by clicking the share link on top of the side panel
 * @memberof uiStateActions
 * @param id - id of the dropdown
 * @public
 */
export declare const showExportDropdown: (id: ShowExportDropdownUpdaterAction['payload']) => Merge<ShowExportDropdownUpdaterAction, {
    type: typeof ActionTypes.SHOW_EXPORT_DROPDOWN;
}>;
/**
 * Hide side panel header dropdown, activated by clicking the share link on top of the side panel
 * @memberof uiStateActions
 * @public
 */
export declare const hideExportDropdown: () => {
    type: typeof ActionTypes.HIDE_EXPORT_DROPDOWN;
};
/** TOGGLE_SIDE_PANEL_CLOSE_BUTTON*/
export declare type ToggleSidePanelCloseButtonUpdaterAction = {
    payload: {
        show: boolean;
    };
};
/**
 * Toggle side panel close button
 * @memberof uiStateActions
 * @param show - if side panel button visible
 * @public
 */
export declare const toggleSidePanelCloseButton: (show: boolean) => Merge<ToggleSidePanelCloseButtonUpdaterAction, {
    type: typeof ActionTypes.TOGGLE_SIDE_PANEL_CLOSE_BUTTON;
}>;
/** TOGGLE_MAP_CONTROL */
export declare type ToggleMapControlUpdaterAction = {
    payload: {
        panelId: string;
        index: number;
    };
};
/**
 * Toggle active map control panel
 * @memberof uiStateActions
 * @param panelId - map control panel id, one of the keys of: [`DEFAULT_MAP_CONTROLS`](#default_map_controls)
 * @public
 */
export declare const toggleMapControl: (panelId: ToggleMapControlUpdaterAction['payload']['panelId'], index: ToggleMapControlUpdaterAction['payload']['index']) => Merge<ToggleMapControlUpdaterAction, {
    type: typeof ActionTypes.TOGGLE_MAP_CONTROL;
}>;
/** SET_MAP_CONTROL_VISIBILITY */
export declare type setMapControlVisibilityUpdaterAction = {
    payload: {
        panelId: string;
        show: boolean;
    };
};
/**
 * Toggle active map control panel
 * @memberof uiStateActions
 * @param panelId - map control panel id, one of the keys of: [`DEFAULT_MAP_CONTROLS`](#default_map_controls)
 * @public
 */
export declare const setMapControlVisibility: (panelId: setMapControlVisibilityUpdaterAction['payload']['panelId'], show: setMapControlVisibilityUpdaterAction['payload']['show']) => Merge<setMapControlVisibilityUpdaterAction, {
    type: typeof ActionTypes.SET_MAP_CONTROL_VISIBILITY;
}>;
/** OPEN_DELETE_MODAL */
export declare type OpenDeleteModalUpdaterAction = {
    payload: string;
};
/**
 * Toggle active map control panel
 * @memberof uiStateActions
 * @param datasetId - `id` of the dataset to be deleted
 * @public
 */
export declare const openDeleteModal: (datasetId: OpenDeleteModalUpdaterAction['payload']) => Merge<OpenDeleteModalUpdaterAction, {
    type: typeof ActionTypes.OPEN_DELETE_MODAL;
}>;
/** ADD_NOTIFICATION */
export declare type AddNotificationUpdaterAction = {
    payload: object;
};
/**
 * Add a notification to be displayed.
 * Existing notification will be updated in case of matching id.
 * @memberof uiStateActions
 * @param notification - The `notification` object to be added or updated
 * @public
 */
export declare const addNotification: (notification: AddNotificationUpdaterAction['payload']) => Merge<AddNotificationUpdaterAction, {
    type: typeof ActionTypes.ADD_NOTIFICATION;
}>;
/** REMOVE_NOTIFICATION */
export declare type RemoveNotificationUpdaterAction = {
    payload: string;
};
/**
 * Remove a notification
 * @memberof uiStateActions
 * @param id - `id` of the notification to be removed
 * @public
 */
export declare const removeNotification: (id: RemoveNotificationUpdaterAction['payload']) => Merge<RemoveNotificationUpdaterAction, {
    type: typeof ActionTypes.REMOVE_NOTIFICATION;
}>;
/** SET_EXPORT_IMAGE_SETTING */
export declare type SetExportImageSettingUpdaterAction = {
    payload: Partial<ExportImage>;
};
/**
 * Set `exportImage` settings: ratio, resolution, legend
 * @memberof uiStateActions
 * @param newSetting - {ratio: '1x'}
 * @public
 */
export declare const setExportImageSetting: (newSetting: SetExportImageSettingUpdaterAction['payload']) => Merge<SetExportImageSettingUpdaterAction, {
    type: typeof ActionTypes.SET_EXPORT_IMAGE_SETTING;
}>;
/**
 * Start exporting image flow
 * @memberof uiStateActions
 * @public
 */
export declare const startExportingImage: (options?: {
    ratio?: string;
    resolution?: string;
    legend?: string;
    center?: boolean;
}) => Merge<SetExportImageSettingUpdaterAction, {
    type: typeof ActionTypes.START_EXPORTING_IMAGE;
}>;
/** SET_EXPORT_IMAGE_DATA_URI */
export declare type SetExportImageDataUriUpdaterAction = {
    payload: string;
};
/**
 * Set `exportImage.setExportImageDataUri` to a dataUri
 * @memberof uiStateActions
 * @param dataUri - export image data uri
 * @public
 */
export declare const setExportImageDataUri: (dataUri: SetExportImageDataUriUpdaterAction['payload']) => Merge<SetExportImageDataUriUpdaterAction, {
    type: typeof ActionTypes.SET_EXPORT_IMAGE_DATA_URI;
}>;
/** SET_EXPORT_IMAGE_ERROR */
export declare type SetExportImageErrorUpdaterAction = {
    payload: Error;
};
/**
 * Set Export image error
 * @memberof uiStateActions
 * @public
 */
export declare const setExportImageError: (error: SetExportImageErrorUpdaterAction['payload']) => Merge<SetExportImageErrorUpdaterAction, {
    type: typeof ActionTypes.SET_EXPORT_IMAGE_ERROR;
}>;
/**
 * Delete cached export image
 * @memberof uiStateActions
 * @public
 */
export declare const cleanupExportImage: () => {
    type: typeof ActionTypes.CLEANUP_EXPORT_IMAGE;
};
/** SET_EXPORT_SELECTED_DATASET */
export declare type SetExportSelectedDatasetUpdaterAction = {
    payload: string;
};
/**
 * Set selected dataset for export
 * @memberof uiStateActions
 * @param datasetId - dataset id
 * @public
 */
export declare const setExportSelectedDataset: (datasetId: SetExportSelectedDatasetUpdaterAction['payload']) => Merge<SetExportSelectedDatasetUpdaterAction, {
    type: typeof ActionTypes.SET_EXPORT_SELECTED_DATASET;
}>;
/** SET_EXPORT_DATA_TYPE */
export declare type SetExportDataTypeUpdaterAction = {
    payload: string;
};
/**
 * Set data format for exporting data
 * @memberof uiStateActions
 * @param dataType - one of `'text/csv'`
 * @public
 */
export declare const setExportDataType: (dataType: SetExportDataTypeUpdaterAction['payload']) => Merge<SetExportDataTypeUpdaterAction, {
    type: typeof ActionTypes.SET_EXPORT_DATA_TYPE;
}>;
/** SET_EXPORT_FILTERED */
export declare type SetExportFilteredUpdaterAction = {
    payload: boolean;
};
/**
 * Whether to export filtered data, `true` or `false`
 * @memberof uiStateActions
 * @param payload - set `true` to ony export filtered data
 * @public
 */
export declare const setExportFiltered: (exportFiltered: SetExportFilteredUpdaterAction['payload']) => Merge<SetExportFilteredUpdaterAction, {
    type: typeof ActionTypes.SET_EXPORT_FILTERED;
}>;
/**
 * Whether to including data in map config, toggle between `true` or `false`
 * @memberof uiStateActions
 * @public
 */
export declare const setExportData: () => {
    type: typeof ActionTypes.SET_EXPORT_DATA;
};
/** SET_USER_MAPBOX_ACCESS_TOKEN */
export declare type SetUserMapboxAccessTokenUpdaterAction = {
    payload: string;
};
/**
 * Whether we export a mapbox access token used to create a single map html file
 * @memberof uiStateActions
 * @param payload - mapbox access token
 * @public
 */
export declare const setUserMapboxAccessToken: (payload: SetUserMapboxAccessTokenUpdaterAction['payload']) => Merge<SetUserMapboxAccessTokenUpdaterAction, {
    type: typeof ActionTypes.SET_USER_MAPBOX_ACCESS_TOKEN;
}>;
/** SET_EXPORT_MAP_FORMAT */
export declare type SetExportMapFormatUpdaterAction = {
    payload: string;
};
/**
 * Set the export map format (html, json)
 * @memberOf uiStateActions
 * @param payload - map format
 * @public
 */
export declare const setExportMapFormat: (mapFormat: SetExportMapFormatUpdaterAction['payload']) => Merge<SetExportMapFormatUpdaterAction, {
    type: typeof ActionTypes.SET_EXPORT_MAP_FORMAT;
}>;
/** SET_EXPORT_MAP_HTML_MODE */
export declare type SetExportHTMLMapModeUpdaterAction = {
    payload: string;
};
/**
 * Set the HTML mode to use to export HTML mode
 * @memberOf uiStateActions
 * @param payload - map mode
 */
export declare const setExportHTMLMapMode: (mode: SetExportHTMLMapModeUpdaterAction['payload']) => Merge<SetExportHTMLMapModeUpdaterAction, {
    type: typeof ActionTypes.SET_EXPORT_MAP_HTML_MODE;
}>;
/** SET_LOCALE */
export declare type SetLocaleUpdaterAction = {
    payload: {
        locale: string;
    };
};
/**
 * Set `locale` value
 * @memberof uiStateActions
 * @param locale - locale of the UI
 * @public
 */
export declare const setLocale: (locale: SetLocaleUpdaterAction['payload']['locale']) => Merge<SetLocaleUpdaterAction, {
    type: typeof ActionTypes.SET_LOCALE;
}>;
/** TOGGLE_PANEL_LIST_VIEW */
export declare type TogglePanelListViewAction = {
    payload: {
        panelId: string;
        listView: string;
    };
};
/**
 * Toggle layer panel list view
 * @memberof uiStateActions
 * @param payload
 * @param payload.panelId panel id.
 * @param payload.listView layer panel listView value. Can be 'list' or 'sortByDataset'
 * @public
 */
export declare const togglePanelListView: (payload: TogglePanelListViewAction['payload']) => Merge<TogglePanelListViewAction, {
    type: typeof ActionTypes.TOGGLE_PANEL_LIST_VIEW;
}>;
