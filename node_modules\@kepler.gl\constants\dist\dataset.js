"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.RemoteTileFormat = exports.REMOTE_TILE = exports.DatasetType = void 0;
// SPDX-License-Identifier: MIT
// Copyright contributors to the kepler.gl project
var DatasetType = exports.DatasetType = /*#__PURE__*/function (DatasetType) {
  DatasetType["LOCAL"] = "local";
  DatasetType["VECTOR_TILE"] = "vector-tile";
  return DatasetType;
}({});
var RemoteTileFormat = exports.RemoteTileFormat = /*#__PURE__*/function (RemoteTileFormat) {
  RemoteTileFormat["MVT"] = "mvt";
  RemoteTileFormat["PMTILES"] = "pmtiles";
  return RemoteTileFormat;
}({});
var REMOTE_TILE = exports.REMOTE_TILE = 'remote';
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJEYXRhc2V0VHlwZSIsImV4cG9ydHMiLCJSZW1vdGVUaWxlRm9ybWF0IiwiUkVNT1RFX1RJTEUiXSwic291cmNlcyI6WyIuLi9zcmMvZGF0YXNldC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBTUERYLUxpY2Vuc2UtSWRlbnRpZmllcjogTUlUXG4vLyBDb3B5cmlnaHQgY29udHJpYnV0b3JzIHRvIHRoZSBrZXBsZXIuZ2wgcHJvamVjdFxuXG5leHBvcnQgZW51bSBEYXRhc2V0VHlwZSB7XG4gIExPQ0FMID0gJ2xvY2FsJyxcbiAgVkVDVE9SX1RJTEUgPSAndmVjdG9yLXRpbGUnXG59XG5cbmV4cG9ydCBlbnVtIFJlbW90ZVRpbGVGb3JtYXQge1xuICBNVlQgPSAnbXZ0JyxcbiAgUE1USUxFUyA9ICdwbXRpbGVzJ1xufVxuXG5leHBvcnQgY29uc3QgUkVNT1RFX1RJTEUgPSAncmVtb3RlJztcblxuZXhwb3J0IHR5cGUgVmVjdG9yVGlsZURhdGFzZXRNZXRhZGF0YSA9IHtcbiAgdHlwZTogdHlwZW9mIFJFTU9URV9USUxFO1xuICByZW1vdGVUaWxlRm9ybWF0OiBSZW1vdGVUaWxlRm9ybWF0O1xuICB0aWxlc2V0RGF0YVVybDogc3RyaW5nO1xuICB0aWxlc2V0TWV0YWRhdGFVcmw/OiBzdHJpbmc7XG59O1xuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBO0FBQUEsSUFFWUEsV0FBVyxHQUFBQyxPQUFBLENBQUFELFdBQUEsMEJBQVhBLFdBQVc7RUFBWEEsV0FBVztFQUFYQSxXQUFXO0VBQUEsT0FBWEEsV0FBVztBQUFBO0FBQUEsSUFLWEUsZ0JBQWdCLEdBQUFELE9BQUEsQ0FBQUMsZ0JBQUEsMEJBQWhCQSxnQkFBZ0I7RUFBaEJBLGdCQUFnQjtFQUFoQkEsZ0JBQWdCO0VBQUEsT0FBaEJBLGdCQUFnQjtBQUFBO0FBS3JCLElBQU1DLFdBQVcsR0FBQUYsT0FBQSxDQUFBRSxXQUFBLEdBQUcsUUFBUSIsImlnbm9yZUxpc3QiOltdfQ==