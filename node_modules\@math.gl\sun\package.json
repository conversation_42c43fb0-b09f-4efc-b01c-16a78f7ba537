{"name": "@math.gl/sun", "description": "Sun classes", "license": "MIT", "publishConfig": {"access": "public"}, "version": "3.6.3", "keywords": ["javascript", "math", "geospatial", "sun", "suncalc", "solar"], "repository": {"type": "git", "url": "https://github.com/uber-web/math.gl.git"}, "types": "dist/index.d.ts", "main": "dist/es5/index.js", "module": "dist/esm/index.js", "files": ["dist", "src"], "dependencies": {"@babel/runtime": "^7.12.0"}, "gitHead": "0efab394df9babad7ed93027c1003f30528b2090"}