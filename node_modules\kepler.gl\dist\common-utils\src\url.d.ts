/**
 * Allows to break down a url into multiple params
 * from http://blog.stevenlevithan.com/archives/parseuri
 */
export declare function parseUri(str: string): {
    [key: string]: any;
};
export declare namespace parseUri {
    var options: {
        strictMode: boolean;
        key: string[];
        q: {
            name: string;
            parser: RegExp;
        };
        parser: {
            strict: RegExp;
            loose: RegExp;
        };
    };
}
