import React from 'react';
import DatasetTagFactory from './dataset-tag';
import { SourceDataSelectorProps } from './types';
declare function SourceDataSelectorContentFactory(DatasetTag: any): ({ className, datasets, dataId, inputTheme, onSelect, defaultValue, disabled }: SourceDataSelectorProps) => React.JSX.Element;
declare namespace SourceDataSelectorContentFactory {
    var deps: (typeof DatasetTagFactory)[];
}
export default SourceDataSelectorContentFactory;
