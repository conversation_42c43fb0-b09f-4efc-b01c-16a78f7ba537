import React, { CSSProperties } from 'react';
import { DraggableAttributes } from '@dnd-kit/core';
import { Transform } from '@dnd-kit/utilities';
import { TooltipFields } from '@kepler.gl/types';
import { BaseComponentProps } from '../../../types';
interface TooltipChickletProps {
    disabled: boolean;
    item: {
        name: string;
    };
    displayOption: (item: any) => string;
    remove: any;
    attributes: DraggableAttributes;
    listeners: any;
    setNodeRef: (node: HTMLElement | null) => void;
    transform: Transform | null;
    transition?: string;
    isDragging: boolean;
}
declare type TooltipConfig = {
    fieldsToShow: {
        [key: string]: {
            name: string;
            format: string | null;
        }[];
    };
    compareMode: boolean;
    compareType: string | null;
};
export declare type IconDivProps = BaseComponentProps & {
    $status?: 'SHOW' | 'ACTIVE' | null;
};
export declare type SortableStyledItemProps = BaseComponentProps & {
    $transition?: CSSProperties['transition'];
    $transform?: CSSProperties['transform'];
    ref: (node: HTMLElement | null) => void;
};
declare function TooltipChickletFactory(dataId: string, config: TooltipConfig, onChange: (cfg: TooltipConfig) => void, fields: TooltipFields[], onDisplayFormatChange: any): React.FC<TooltipChickletProps>;
export default TooltipChickletFactory;
