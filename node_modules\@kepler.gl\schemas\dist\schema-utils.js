"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getPropertyValueFromSchema = getPropertyValueFromSchema;
exports.loadPropertiesOrApplySchema = loadPropertiesOrApplySchema;
exports.savePropertiesOrApplySchema = savePropertiesOrApplySchema;
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2["default"])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
// SPDX-License-Identifier: MIT
// Copyright contributors to the kepler.gl project

/**
 * Recursively save / load value for state based on property keys,
 * if property[key] is another schema
 * Use is to get value to save
 * @param {Object} state - state to save
 * @param {Object} properties - properties schema
 * @returns {Object} - saved state
 */
function savePropertiesOrApplySchema(state, properties) {
  return getPropertyValueFromSchema('save', state, properties);
}
function loadPropertiesOrApplySchema(state, properties) {
  return getPropertyValueFromSchema('load', state, properties);
}
function getPropertyValueFromSchema(operation, state, properties) {
  return Object.keys(properties).reduce(function (accu, key) {
    return _objectSpread(_objectSpread({}, accu), key in state ? properties[key] ?
    // if it's another schema
    properties[key][operation] ?
    // call save or load
    properties[key][operation](state[key], state) :
    // if it's another property object
    getPropertyValueFromSchema(operation, state[key], properties[key]) : (0, _defineProperty2["default"])({}, key, state[key]) : {});
  }, {});
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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