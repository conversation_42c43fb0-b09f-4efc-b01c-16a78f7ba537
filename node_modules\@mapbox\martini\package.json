{"name": "@mapbox/martini", "version": "0.2.0", "description": "A JavaScript library for real-time terrain mesh generation", "main": "martini.js", "unpkg": "martini.min.js", "module": "index.js", "scripts": {"pretest": "eslint index.js bench.js test", "test": "node -r esm test/test.js", "bench": "node -r esm bench.js", "build": "rollup -c", "prepublishOnly": "npm run test && npm run build"}, "keywords": ["terrain", "rtin", "mesh", "3d", "webgl"], "author": "<PERSON>", "license": "ISC", "devDependencies": {"eslint": "^6.8.0", "eslint-config-mourner": "^3.0.0", "esm": "^3.2.25", "pngjs": "^3.4.0", "rollup": "^1.31.0", "rollup-plugin-terser": "^5.2.0", "tape": "^4.13.0"}, "files": ["index.js", "martini.js", "martini.min.js"], "eslintConfig": {"extends": "mourner", "rules": {"no-use-before-define": 0}}}