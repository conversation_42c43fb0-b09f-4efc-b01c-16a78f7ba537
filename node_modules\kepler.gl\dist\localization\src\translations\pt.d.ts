declare const _default: {
    property: {
        weight: string;
        label: string;
        fillColor: string;
        color: string;
        strokeColor: string;
        radius: string;
        outline: string;
        stroke: string;
        density: string;
        height: string;
        sum: string;
        pointCount: string;
    };
    placeholder: {
        search: string;
        selectField: string;
        yAxis: string;
        selectType: string;
        selectValue: string;
        enterValue: string;
        empty: string;
    };
    misc: {
        by: string;
        valuesIn: string;
        valueEquals: string;
        dataSource: string;
        brushRadius: string;
        empty: string;
    };
    mapLayers: {
        title: string;
        label: string;
        road: string;
        border: string;
        building: string;
        water: string;
        land: string;
        '3dBuilding': string;
        background: string;
    };
    panel: {
        text: {
            label: string;
            labelWithId: string;
            fontSize: string;
            fontColor: string;
            textAnchor: string;
            alignment: string;
            addMoreLabel: string;
        };
    };
    sidebar: {
        panels: {
            layer: string;
            filter: string;
            interaction: string;
            basemap: string;
        };
    };
    layer: {
        required: string;
        radius: string;
        color: string;
        fillColor: string;
        outline: string;
        weight: string;
        propertyBasedOn: string;
        coverage: string;
        stroke: string;
        strokeWidth: string;
        strokeColor: string;
        basic: string;
        trailLength: string;
        trailLengthDescription: string;
        newLayer: string;
        elevationByDescription: string;
        colorByDescription: string;
        aggregateBy: string;
        '3DModel': string;
        '3DModelOptions': string;
        type: {
            point: string;
            arc: string;
            line: string;
            grid: string;
            hexbin: string;
            polygon: string;
            geojson: string;
            cluster: string;
            icon: string;
            heatmap: string;
            hexagon: string;
            hexagonid: string;
            trip: string;
            s2: string;
            '3d': string;
        };
        layerUpdateError: string;
    };
    layerVisConfigs: {
        strokeWidth: string;
        strokeWidthRange: string;
        radius: string;
        fixedRadius: string;
        fixedRadiusDescription: string;
        radiusRange: string;
        clusterRadius: string;
        radiusRangePixels: string;
        opacity: string;
        coverage: string;
        outline: string;
        colorRange: string;
        stroke: string;
        strokeColor: string;
        strokeColorRange: string;
        targetColor: string;
        colorAggregation: string;
        heightAggregation: string;
        resolutionRange: string;
        sizeScale: string;
        worldUnitSize: string;
        elevationScale: string;
        enableElevationZoomFactor: string;
        enableElevationZoomFactorDescription: string;
        enableHeightZoomFactor: string;
        heightScale: string;
        coverageRange: string;
        highPrecisionRendering: string;
        highPrecisionRenderingDescription: string;
        height: string;
        heightDescription: string;
        fill: string;
        enablePolygonHeight: string;
        showWireframe: string;
        weightIntensity: string;
        zoomScale: string;
        heightRange: string;
        heightMultiplier: string;
    };
    layerManager: {
        addData: string;
        addLayer: string;
        layerBlending: string;
    };
    mapManager: {
        mapStyle: string;
        addMapStyle: string;
        '3dBuildingColor': string;
        backgroundColor: string;
    };
    layerConfiguration: {
        defaultDescription: string;
        howTo: string;
    };
    filterManager: {
        addFilter: string;
    };
    datasetTitle: {
        showDataTable: string;
        removeDataset: string;
    };
    datasetInfo: {
        rowCount: string;
    };
    tooltip: {
        hideLayer: string;
        showLayer: string;
        hideFeature: string;
        showFeature: string;
        hide: string;
        show: string;
        removeLayer: string;
        resetAfterError: string;
        layerSettings: string;
        closePanel: string;
        switchToDualView: string;
        showLegend: string;
        disable3DMap: string;
        DrawOnMap: string;
        selectLocale: string;
        hideLayerPanel: string;
        showLayerPanel: string;
        moveToTop: string;
        selectBaseMapStyle: string;
        delete: string;
        timePlayback: string;
        cloudStorage: string;
        '3DMap': string;
    };
    toolbar: {
        en: string;
        fi: string;
        pt: string;
        es: string;
        ca: string;
        ja: string;
        cn: string;
        ru: string;
        exportImage: string;
        exportData: string;
        exportMap: string;
        shareMapURL: string;
        saveMap: string;
        select: string;
        polygon: string;
        rectangle: string;
        hide: string;
        show: string;
    };
    modal: {
        title: {
            deleteDataset: string;
            addDataToMap: string;
            exportImage: string;
            exportData: string;
            exportMap: string;
            addCustomMapboxStyle: string;
            saveMap: string;
            shareURL: string;
        };
        button: {
            delete: string;
            download: string;
            export: string;
            addStyle: string;
            save: string;
            defaultCancel: string;
            defaultConfirm: string;
        };
        exportImage: {
            ratioTitle: string;
            ratioDescription: string;
            ratioOriginalScreen: string;
            ratioCustom: string;
            ratio4_3: string;
            ratio16_9: string;
            resolutionTitle: string;
            resolutionDescription: string;
            mapLegendTitle: string;
            mapLegendAdd: string;
        };
        exportData: {
            datasetTitle: string;
            datasetSubtitle: string;
            allDatasets: string;
            dataTypeTitle: string;
            dataTypeSubtitle: string;
            filterDataTitle: string;
            filterDataSubtitle: string;
            filteredData: string;
            unfilteredData: string;
            fileCount: string;
            rowCount: string;
        };
        deleteData: {
            warning: string;
        };
        addStyle: {
            publishTitle: string;
            publishSubtitle1: string;
            publishSubtitle2: string;
            publishSubtitle3: string;
            publishSubtitle4: string;
            publishSubtitle5: string;
            publishSubtitle6: string;
            publishSubtitle7: string;
            exampleToken: string;
            pasteTitle: string;
            pasteSubtitle1: string;
            pasteSubtitle2: string;
            namingTitle: string;
        };
        shareMap: {
            shareUriTitle: string;
            shareUriSubtitle: string;
            cloudTitle: string;
            cloudSubtitle: string;
            shareDisclaimer: string;
            gotoPage: string;
        };
        statusPanel: {
            mapUploading: string;
            error: string;
        };
        saveMap: {
            title: string;
            subtitle: string;
        };
        exportMap: {
            formatTitle: string;
            formatSubtitle: string;
            html: {
                selection: string;
                tokenTitle: string;
                tokenSubtitle: string;
                tokenPlaceholder: string;
                tokenMisuseWarning: string;
                tokenDisclaimer: string;
                tokenUpdate: string;
                modeTitle: string;
                modeSubtitle1: string;
                modeSubtitle2: string;
                modeDescription: string;
                read: string;
                edit: string;
            };
            json: {
                configTitle: string;
                configDisclaimer: string;
                selection: string;
                disclaimer: string;
            };
        };
        loadingDialog: {
            loading: string;
        };
        loadData: {
            upload: string;
            storage: string;
        };
        tripInfo: {
            title: string;
            description1: string;
            code: string;
            description2: string;
            example: string;
        };
        iconInfo: {
            title: string;
            description1: string;
            code: string;
            description2: string;
            example: string;
            icons: string;
        };
        storageMapViewer: {
            lastModified: string;
            back: string;
        };
        overwriteMap: {
            title: string;
            alreadyExists: string;
        };
        loadStorageMap: {
            back: string;
            goToPage: string;
            storageMaps: string;
            noSavedMaps: string;
        };
    };
    header: {
        visibleLayers: string;
        layerLegend: string;
    };
    interactions: {
        tooltip: string;
        brush: string;
        coordinate: string;
    };
    layerBlending: {
        title: string;
        additive: string;
        normal: string;
        subtractive: string;
    };
    columns: {
        title: string;
        lat: string;
        lng: string;
        altitude: string;
        icon: string;
        geojson: string;
        arc: {
            lat0: string;
            lng0: string;
            lat1: string;
            lng1: string;
        };
        line: {
            alt0: string;
            alt1: string;
        };
        grid: {
            worldUnitSize: string;
        };
        hexagon: {
            worldUnitSize: string;
        };
    };
    color: {
        customPalette: string;
        steps: string;
        type: string;
        reversed: string;
    };
    scale: {
        colorScale: string;
        sizeScale: string;
        strokeScale: string;
        scale: string;
    };
    fileUploader: {
        message: string;
        chromeMessage: string;
        disclaimer: string;
        configUploadMessage: string;
        browseFiles: string;
        uploading: string;
        fileNotSupported: string;
        or: string;
    };
    density: string;
    'Bug Report': string;
    'User Guide': string;
    Save: string;
    Share: string;
};
export default _default;
