// SPDX-License-Identifier: MIT
// Copyright contributors to the kepler.gl project

import React, {Component} from 'react';
import PropTypes from 'prop-types';
import Base from '../base';

export default class HueSaturation extends Component {
  static propTypes = {
    /** Set the height of the icon, ex. '16px' */
    height: PropTypes.string
  };

  static defaultProps = {
    height: '16px',
    viewBox: '0 0 16 16',
    predefinedClassName: 'data-ex-icons-hue-saturation'
  };

  render() {
    return (
      <Base {...this.props}>
        <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
          <g clipPath="url(#clip0_129_11265)">
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M3.87106 6.06729C3.87098 6.05614 3.87093 6.04499 3.87093 6.03382C3.87093 3.75342 5.71956 1.90479 7.99996 1.90479C10.2804 1.90479 12.129 3.75342 12.129 6.03382C12.129 6.04496 12.129 6.05609 12.1289 6.06721C13.3087 6.79404 14.0953 8.0978 14.0953 9.58527C14.0953 11.8657 12.2467 13.7143 9.96625 13.7143C9.25441 13.7143 8.58464 13.5342 8.00003 13.217C7.41543 13.5342 6.74566 13.7143 6.03382 13.7143C3.75342 13.7143 1.90479 11.8657 1.90479 9.58527C1.90479 8.09786 2.69127 6.79414 3.87106 6.06729ZM9.36787 9.11148C8.94989 9.29754 8.487 9.40094 7.99996 9.40094C7.51297 9.40094 7.05013 9.29756 6.63219 9.11154C6.76297 8.18272 7.27272 7.37559 8.00003 6.85155C8.72733 7.37558 9.23707 8.18269 9.36787 9.11148ZM9.384 9.92517C8.95134 10.0791 8.48543 10.1628 7.99996 10.1628C7.51455 10.1628 7.04869 10.0791 6.61607 9.92522C6.71482 10.9102 7.23801 11.77 8.00003 12.319C8.76207 11.7699 9.28527 10.9101 9.384 9.92517ZM5.93393 8.69278C6.12934 7.8059 6.60989 7.026 7.27454 6.45411C6.89059 6.30184 6.47198 6.21815 6.03382 6.21815C5.54678 6.21815 5.08389 6.32155 4.66591 6.50761C4.79064 7.39331 5.25996 8.16835 5.93393 8.69278ZM3.96766 6.9264C4.21174 8.03404 4.90058 8.97481 5.83735 9.55189C5.83726 9.563 5.83722 9.57413 5.83722 9.58527C5.83722 10.8373 6.3945 11.9592 7.27454 12.7164C6.89059 12.8687 6.47198 12.9524 6.03382 12.9524C4.1742 12.9524 2.66669 11.4449 2.66669 9.58527C2.66669 8.50441 3.17597 7.5425 3.96766 6.9264ZM4.64978 5.69392C4.82019 3.99393 6.25508 2.66669 7.99996 2.66669C9.74483 2.66669 11.1797 3.9939 11.3501 5.69387C10.9175 5.54 10.4517 5.45624 9.96625 5.45624C9.25441 5.45624 8.58464 5.63638 8.00003 5.95355C7.41543 5.63638 6.74566 5.45624 6.03382 5.45624C5.54835 5.45624 5.08244 5.54002 4.64978 5.69392ZM11.334 6.50755C10.9161 6.32153 10.4532 6.21815 9.96625 6.21815C9.52809 6.21815 9.10948 6.30184 8.72553 6.45411C9.39016 7.02598 9.87069 7.80585 10.0661 8.69269C10.74 8.16825 11.2093 7.39322 11.334 6.50755ZM12.0323 6.92631C11.7882 8.03394 11.0994 8.9747 10.1627 9.5518C10.1628 9.56295 10.1629 9.5741 10.1629 9.58527C10.1629 10.8373 9.60557 11.9592 8.72553 12.7164C9.10948 12.8687 9.52809 12.9524 9.96625 12.9524C11.8259 12.9524 13.3334 11.4449 13.3334 9.58527C13.3334 8.50435 12.824 7.5424 12.0323 6.92631Z"
            />
          </g>
          <defs>
            <clipPath id="clip0_129_11265">
              <rect width="16" height="16" />
            </clipPath>
          </defs>
        </svg>
      </Base>
    );
  }
}
