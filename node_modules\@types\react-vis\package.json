{"name": "@types/react-vis", "version": "1.11.7", "description": "TypeScript definitions for react-vis", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-vis", "license": "MIT", "contributors": [{"name": "Domino987", "url": "https://github.com/Domino987", "githubUsername": "Domino987"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-vis"}, "scripts": {}, "dependencies": {"@types/react": "*"}, "typesPublisherContentHash": "b8f376251985fee640a03ad781f29d4242091b9ffee11e914a03223b53cb9b9e", "typeScriptVersion": "3.9"}