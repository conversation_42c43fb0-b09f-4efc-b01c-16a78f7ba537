import React from 'react';
import { IntlShape } from 'react-intl';
import DatasetTagFactory from '../common/dataset-tag';
import FieldSelectorFactory from '../../common/field-selector';
import { Datasets } from '@kepler.gl/table';
declare type TooltipConfigProps = {
    config: {
        fieldsToShow: {
            [key: string]: {
                name: string;
                format: string | null;
            }[];
        };
        compareMode: boolean;
        compareType: string | null;
    };
    onChange: (config: {
        fieldsToShow: {
            [key: string]: {
                name: string;
                format: string | null;
            }[];
        };
        compareMode: boolean;
        compareType: string | null;
    }) => void;
    datasets: Datasets;
    intl: IntlShape;
    onDisplayFormatChange: (dataId: any, column: any, displayFormat: any) => void;
};
declare function TooltipConfigFactory(DatasetTag: ReturnType<typeof DatasetTagFactory>, FieldSelector: ReturnType<typeof FieldSelectorFactory>): React.FC<import("react-intl").WithIntlProps<TooltipConfigProps>> & {
    WrappedComponent: React.ComponentType<TooltipConfigProps>;
};
declare namespace TooltipConfigFactory {
    var deps: (typeof FieldSelectorFactory | typeof DatasetTagFactory)[];
}
export default TooltipConfigFactory;
