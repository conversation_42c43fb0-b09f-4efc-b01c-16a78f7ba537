// loaders.gl
// SPDX-License-Identifier: MIT
// Copyright (c) vis.gl contributors
// __VERSION__ is injected by babel-plugin-version-inline
// @ts-ignore TS2304: Cannot find name '__VERSION__'.
const VERSION = typeof "4.3.2" !== 'undefined' ? "4.3.2" : 'latest';
/**
 * Loads any data and returns null (or optionally passes through data unparsed)
 */
export const NullWorkerLoader = {
    dataType: null,
    batchType: null,
    name: 'Null loader',
    id: 'null',
    module: 'core',
    version: VERSION,
    worker: true,
    mimeTypes: ['application/x.empty'],
    extensions: ['null'],
    tests: [() => false],
    options: {
        null: {}
    }
};
/**
 * Loads any data and returns null (or optionally passes through data unparsed)
 */
export const NullLoader = {
    dataType: null,
    batchType: null,
    name: 'Null loader',
    id: 'null',
    module: 'core',
    version: VERSION,
    mimeTypes: ['application/x.empty'],
    extensions: ['null'],
    parse: async (arrayBuffer, options, context) => parseSync(arrayBuffer, options || {}, context),
    parseSync,
    parseInBatches: async function* generator(asyncIterator, options, context) {
        for await (const batch of asyncIterator) {
            yield parseSync(batch, options, context);
        }
    },
    tests: [() => false],
    options: {
        null: {}
    }
};
/**
 * Returns arguments passed to the parse API in a format that can be transferred to a
 * web worker. The `context` parameter is stripped using JSON.stringify & parse.
 */
function parseSync(arrayBuffer, options, context) {
    return null;
}
