// SPDX-License-Identifier: MIT
// Copyright contributors to the kepler.gl project

import React from 'react';
import Base, {BaseProps} from './base';

const OrderByList = ({
  height = '20px',
  fill = 'currentColor',
  viewBox = '0 0 24 24',
  predefinedClassName = 'data-ex-icons-order-by-list',
  ...restProps
}: Partial<BaseProps>) => {
  const props = {
    height,
    fill,
    viewBox,
    predefinedClassName,
    ...restProps
  };
  return (
    <Base {...props}>
      <path
        d="M8.55556 14C8.55556 14.3516 8.45129 14.6953 8.25595 14.9877C8.0606 15.28 7.78295 15.5079 7.4581 15.6425C7.13326 15.777 6.77581 15.8122 6.43095 15.7436C6.0861 15.675 5.76933 15.5057 5.5207 15.2571C5.27207 15.0085 5.10276 14.6917 5.03416 14.3468C4.96556 14.002 5.00077 13.6445 5.13533 13.3197C5.26988 12.9948 5.49774 12.7172 5.7901 12.5218C6.08245 12.3265 6.42617 12.2222 6.77778 12.2222C7.24927 12.2222 7.70146 12.4095 8.03486 12.7429C8.36826 13.0763 8.55556 13.5285 8.55556 14ZM23.3333 14C23.3333 13.7643 23.2397 13.5382 23.073 13.3715C22.9063 13.2048 22.6802 13.1111 22.4444 13.1111H12.1111C11.8754 13.1111 11.6493 13.2048 11.4826 13.3715C11.3159 13.5382 11.2222 13.7643 11.2222 14C11.2222 14.2357 11.3159 14.4618 11.4826 14.6285C11.6493 14.7952 11.8754 14.8889 12.1111 14.8889H22.4444C22.6802 14.8889 22.9063 14.7952 23.073 14.6285C23.2397 14.4618 23.3333 14.2357 23.3333 14ZM6.77778 6C6.42617 6 6.08245 6.10426 5.7901 6.29961C5.49774 6.49495 5.26988 6.7726 5.13533 7.09745C5.00077 7.4223 4.96556 7.77975 5.03416 8.1246C5.10276 8.46946 5.27207 8.78623 5.5207 9.03486C5.76933 9.28348 6.0861 9.4528 6.43095 9.5214C6.77581 9.58999 7.13326 9.55479 7.4581 9.42023C7.78295 9.28567 8.0606 9.05781 8.25595 8.76546C8.45129 8.4731 8.55556 8.12939 8.55556 7.77778C8.55556 7.30628 8.36826 6.8541 8.03486 6.5207C7.70146 6.1873 7.24927 6 6.77778 6ZM23.3333 7.77778C23.3333 7.54203 23.2397 7.31594 23.073 7.14924C22.9063 6.98254 22.6802 6.88889 22.4444 6.88889H12.1111C11.8754 6.88889 11.6493 6.98254 11.4826 7.14924C11.3159 7.31594 11.2222 7.54203 11.2222 7.77778C11.2222 8.01353 11.3159 8.23962 11.4826 8.40632C11.6493 8.57302 11.8754 8.66667 12.1111 8.66667H22.4444C22.6802 8.66667 22.9063 8.57302 23.073 8.40632C23.2397 8.23962 23.3333 8.01353 23.3333 7.77778ZM6.77778 18.4444C6.42617 18.4444 6.08245 18.5487 5.7901 18.7441C5.49774 18.9394 5.26988 19.217 5.13533 19.5419C5.00077 19.8667 4.96556 20.2242 5.03416 20.569C5.10276 20.9139 5.27207 21.2307 5.5207 21.4793C5.76933 21.7279 6.0861 21.8972 6.43095 21.9658C6.77581 22.0344 7.13326 21.9992 7.4581 21.8647C7.78295 21.7301 8.0606 21.5023 8.25595 21.2099C8.45129 20.9175 8.55556 20.5738 8.55556 20.2222C8.55556 19.7507 8.36826 19.2985 8.03486 18.9651C7.70146 18.6317 7.24927 18.4444 6.77778 18.4444ZM23.3333 20.2222C23.3333 19.9865 23.2397 19.7604 23.073 19.5937C22.9063 19.427 22.6802 19.3333 22.4444 19.3333H12.1111C11.8754 19.3333 11.6493 19.427 11.4826 19.5937C11.3159 19.7604 11.2222 19.9865 11.2222 20.2222C11.2222 20.458 11.3159 20.6841 11.4826 20.8508C11.6493 21.0175 11.8754 21.1111 12.1111 21.1111H22.4444C22.6802 21.1111 22.9063 21.0175 23.073 20.8508C23.2397 20.6841 23.3333 20.458 23.3333 20.2222Z"
        fill={props.fill}
      />
    </Base>
  );
};

export default OrderByList;
