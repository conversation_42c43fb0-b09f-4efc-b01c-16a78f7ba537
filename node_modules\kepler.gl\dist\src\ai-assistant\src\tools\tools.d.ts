/// <reference types="react" />
import { VisState } from '@kepler.gl/schemas';
import { Dispatch } from 'redux';
import { AiAssistantState } from '../reducers';
export declare function setupLLMTools({ visState, aiAssistant, dispatch }: {
    visState: VisState;
    aiAssistant: AiAssistantState;
    dispatch: Dispatch;
}): {
    filterDataset: import("@openassistant/duckdb").LocalQueryTool;
    genericQuery: import("@openassistant/duckdb").LocalQueryTool;
    classifyTool: import("@openassistant/utils").ExtendedTool<import("@openassistant/geoda").DataClassifyFunctionArgs, import("@openassistant/geoda").DataClassifyLlmResult, import("@openassistant/geoda").DataClassifyAdditionalData, import("@openassistant/geoda").DataClassifyFunctionContext>;
    weightsTool: import("@openassistant/utils").ExtendedTool<import("@openassistant/geoda").SpatialWeightsFunctionArgs, import("@openassistant/geoda").SpatialWeightsLlmResult, import("@openassistant/geoda").SpatialWeightsAdditionalData, import("@openassistant/geoda").SpatialWeightsFunctionContext>;
    globalMoranTool: import("@openassistant/utils").ExtendedTool<import("@openassistant/geoda").MoranScatterPlotFunctionArgs, import("@openassistant/geoda").MoranScatterPlotLlmResult, import("@openassistant/geoda").MoranScatterPlotAdditionalData, import("@openassistant/geoda").MoranScatterPlotFunctionContext>;
    regressionTool: import("@openassistant/utils").ExtendedTool<import("@openassistant/geoda").SpatialRegressionFunctionArgs, import("@openassistant/geoda").SpatialRegressionLlmResult, import("@openassistant/geoda").SpatialRegressionAdditionalData, import("@openassistant/geoda").SpatialRegressionFunctionContext>;
    lisaTool: import("@openassistant/utils").ExtendedTool<import("@openassistant/geoda").LisaFunctionArgs, import("@openassistant/geoda").LisaLlmResult, import("@openassistant/geoda").LisaAdditionalData, import("@openassistant/geoda").LisaFunctionContext>;
    spatialJoinTool: import("@openassistant/utils").ExtendedTool<import("@openassistant/geoda").SpatialJoinFunctionArgs, import("@openassistant/geoda").SpatialJoinLlmResult, import("@openassistant/geoda").SpatialJoinAdditionalData, import("@openassistant/geoda").SpatialJoinFunctionContext>;
    spatialFilterTool: {
        context: {
            getValues: (datasetName: string, variableName: string) => Promise<number[]>;
            getGeometries: (datasetName: string) => Promise<import("@geoda/core").SpatialGeometry>;
            saveAsDataset?: ((datasetName: string, data: Record<string, number[]>) => void) | undefined;
        };
        component: (props: any) => import("react").JSX.Element;
        description: string;
        parameters: import("@openassistant/geoda").SpatialJoinFunctionArgs;
        execute: import("@openassistant/utils").ExecuteFunction<import("@openassistant/geoda").SpatialJoinFunctionArgs, import("@openassistant/geoda").SpatialJoinLlmResult, import("@openassistant/geoda").SpatialJoinAdditionalData, import("@openassistant/geoda").SpatialJoinFunctionContext>;
        priority?: number | undefined;
    };
    bufferTool: import("@openassistant/utils").ExtendedTool<import("@openassistant/geoda").BufferFunctionArgs, import("@openassistant/geoda").BufferLlmResult, import("@openassistant/geoda").BufferAdditionalData, import("@openassistant/geoda").SpatialToolContext>;
    centroidTool: import("@openassistant/utils").ExtendedTool<import("@openassistant/geoda").CentroidFunctionArgs, import("@openassistant/geoda").CentroidLlmResult, import("@openassistant/geoda").CentroidAdditionalData, import("@openassistant/geoda").SpatialToolContext>;
    dissolveTool: import("@openassistant/utils").ExtendedTool<import("@openassistant/geoda").DissolveFunctionArgs, import("@openassistant/geoda").DissolveLlmResult, import("@openassistant/geoda").DissolveAdditionalData, import("@openassistant/geoda").SpatialToolContext>;
    lengthTool: {
        context: {
            getGeometries: (datasetName: string) => Promise<import("@geoda/core").SpatialGeometry>;
        };
        description: string;
        parameters: import("zod").ZodObject<{
            geojson: import("zod").ZodOptional<import("zod").ZodString>;
            datasetName: import("zod").ZodOptional<import("zod").ZodString>;
            distanceUnit: import("zod").ZodDefault<import("zod").ZodEnum<["KM", "Mile"]>>;
        }, "strip", import("zod").ZodTypeAny, {
            distanceUnit: "KM" | "Mile";
            datasetName?: string | undefined;
            geojson?: string | undefined;
        }, {
            datasetName?: string | undefined;
            geojson?: string | undefined;
            distanceUnit?: "KM" | "Mile" | undefined;
        }>;
        execute: import("@openassistant/utils").ExecuteFunction<import("zod").ZodObject<{
            geojson: import("zod").ZodOptional<import("zod").ZodString>;
            datasetName: import("zod").ZodOptional<import("zod").ZodString>;
            distanceUnit: import("zod").ZodDefault<import("zod").ZodEnum<["KM", "Mile"]>>;
        }, "strip", import("zod").ZodTypeAny, {
            distanceUnit: "KM" | "Mile";
            datasetName?: string | undefined;
            geojson?: string | undefined;
        }, {
            datasetName?: string | undefined;
            geojson?: string | undefined;
            distanceUnit?: "KM" | "Mile" | undefined;
        }>, {
            success: boolean;
            result: string;
            lengths: number[];
            distanceUnit: "KM" | "Mile";
        }, never, {
            getGeometries: () => void;
        }>;
        component?: import("react").ElementType<any, keyof import("react").JSX.IntrinsicElements> | undefined;
        priority?: number | undefined;
    };
    areaTool: {
        context: {
            getGeometries: (datasetName: string) => Promise<import("@geoda/core").SpatialGeometry>;
            getValues?: import("@openassistant/geoda").GetValues | undefined;
        };
        description: string;
        parameters: import("@openassistant/geoda").AreaFunctionArgs;
        execute: import("@openassistant/utils").ExecuteFunction<import("@openassistant/geoda").AreaFunctionArgs, import("@openassistant/geoda").AreaLlmResult, import("@openassistant/geoda").AreaAdditionalData, import("@openassistant/geoda").SpatialToolContext>;
        component?: import("react").ElementType<any, keyof import("react").JSX.IntrinsicElements> | undefined;
        priority?: number | undefined;
    };
    perimeterTool: {
        context: {
            getGeometries: (datasetName: string) => Promise<import("@geoda/core").SpatialGeometry>;
            getValues?: import("@openassistant/geoda").GetValues | undefined;
        };
        description: string;
        parameters: import("@openassistant/geoda").PerimeterFunctionArgs;
        execute: import("@openassistant/utils").ExecuteFunction<import("@openassistant/geoda").PerimeterFunctionArgs, import("@openassistant/geoda").PerimeterLlmResult, import("@openassistant/geoda").PerimeterAdditionalData, import("@openassistant/geoda").SpatialToolContext>;
        component?: import("react").ElementType<any, keyof import("react").JSX.IntrinsicElements> | undefined;
        priority?: number | undefined;
    };
    getUsStateGeojson: import("@openassistant/utils").ExtendedTool<import("@openassistant/osm").GetUsStateGeojsonFunctionArgs, import("@openassistant/osm").GetUsStateGeojsonLlmResult, import("@openassistant/osm").GetUsStateGeojsonAdditionalData, object>;
    getUsCountyGeojson: import("@openassistant/utils").ExtendedTool<import("@openassistant/osm").GetUsCountyGeojsonFunctionArgs, import("@openassistant/osm").GetUsCountyGeojsonLlmResult, import("@openassistant/osm").GetUsCountyGeojsonAdditionalData, object>;
    getUsZipcodeGeojson: import("@openassistant/utils").ExtendedTool<import("@openassistant/osm").GetUsZipcodeGeojsonFunctionArgs, import("@openassistant/osm").GetUsZipcodeGeojsonLlmResult, import("@openassistant/osm").GetUsZipcodeGeojsonAdditionalData, object>;
    queryUSZipcodes: import("@openassistant/utils").ExtendedTool<import("@openassistant/osm").QueryZipcodeFunctionArgs, import("@openassistant/osm").QueryZipcodeLlmResult, import("@openassistant/osm").QueryZipcodeAdditionalData, never>;
    geocoding: import("@openassistant/utils").ExtendedTool<import("@openassistant/osm").GeocodingFunctionArgs, import("@openassistant/osm").GeocodingLlmResult, import("@openassistant/osm").GeocodingAdditionalData, object>;
    routing: import("@openassistant/utils").ExtendedTool<import("@openassistant/osm").RoutingFunctionArgs, import("@openassistant/osm").RoutingLlmResult, import("@openassistant/osm").RoutingAdditionalData, import("@openassistant/osm/dist/register-tools").OsmToolContext>;
    isochrone: import("@openassistant/utils").ExtendedTool<import("@openassistant/osm").IsochroneFunctionArgs, import("@openassistant/osm").IsochroneLlmResult, import("@openassistant/osm").IsochroneAdditionalData, import("@openassistant/osm/dist/register-tools").OsmToolContext>;
    roads: {
        context: {
            getGeometries: (datasetName: string) => Promise<import("@geoda/core").SpatialGeometry>;
        };
        description: string;
        parameters: import("@openassistant/osm").RoadsFunctionArgs;
        execute: import("@openassistant/utils").ExecuteFunction<import("@openassistant/osm").RoadsFunctionArgs, import("@openassistant/osm").RoadsLlmResult, import("@openassistant/osm").RoadsAdditionalData, import("@openassistant/osm").RoadsToolContext>;
        component?: import("react").ElementType<any, keyof import("react").JSX.IntrinsicElements> | undefined;
        priority?: number | undefined;
    };
    boxplotTool: import("@openassistant/utils").ExtendedTool<import("@openassistant/echarts").BoxplotToolArgs, import("@openassistant/echarts").BoxplotLlmResult, import("@openassistant/echarts").BoxplotAdditionalData, import("@openassistant/echarts").EChartsToolContext>;
    bubbleChartTool: import("@openassistant/utils").ExtendedTool<import("@openassistant/echarts").BubbleChartToolArgs, import("@openassistant/echarts").BubbleChartLlmResult, import("@openassistant/echarts").BubbleChartAdditionalData, import("@openassistant/echarts").EChartsToolContext>;
    histogramTool: import("@openassistant/utils").ExtendedTool<import("@openassistant/echarts").HistogramToolArgs, import("@openassistant/echarts").HistogramLlmResult, import("@openassistant/echarts").HistogramAdditionalData, import("@openassistant/echarts").EChartsToolContext>;
    pcpTool: import("@openassistant/utils").ExtendedTool<import("@openassistant/echarts").PCPFunctionArgs, import("@openassistant/echarts").PCPLlmResult, import("@openassistant/echarts").PCPAdditionalData, import("@openassistant/echarts").EChartsToolContext>;
    scatterplotTool: import("@openassistant/utils").ExtendedTool<import("@openassistant/echarts").ScatterplotFunctionArgs, import("@openassistant/echarts").ScatterplotLlmResult, import("@openassistant/echarts").ScatterplotAdditionalData, import("@openassistant/echarts").EChartsToolContext>;
    basemap: import("@openassistant/utils").ExtendedTool<import("zod").ZodObject<{
        styleType: import("zod").ZodEnum<["no_map", "dark-matter", "positron", "voyager", "satellite", "dark", "light", "muted", "muted_night"]>;
    }, "strip", import("zod").ZodTypeAny, {
        styleType: "light" | "dark" | "no_map" | "dark-matter" | "positron" | "voyager" | "satellite" | "muted" | "muted_night";
    }, {
        styleType: "light" | "dark" | "no_map" | "dark-matter" | "positron" | "voyager" | "satellite" | "muted" | "muted_night";
    }>, {
        success: boolean;
        styleType: string;
        details?: string | undefined;
        instruction?: string | undefined;
    }, {
        styleType: string;
    }, never>;
    addLayer: import("@openassistant/utils").ExtendedTool<import("zod").ZodObject<{
        datasetName: import("zod").ZodString;
        fieldName: import("zod").ZodString;
        layerType: import("zod").ZodEnum<["point", "arc", "line", "grid", "hexagon", "geojson", "cluster", "heatmap", "h3", "trip", "s2"]>;
        colorScale: import("zod").ZodOptional<import("zod").ZodEnum<["quantile", "quantize", "ordinal", "custom"]>>;
        customColorScale: import("zod").ZodOptional<import("zod").ZodArray<import("zod").ZodNumber, "many">>;
    }, import("zod").UnknownKeysParam, import("zod").ZodTypeAny, {
        datasetName: string;
        fieldName: string;
        layerType: "grid" | "h3" | "line" | "geojson" | "point" | "arc" | "hexagon" | "cluster" | "heatmap" | "trip" | "s2";
        colorScale?: "custom" | "quantile" | "quantize" | "ordinal" | undefined;
        customColorScale?: number[] | undefined;
    }, {
        datasetName: string;
        fieldName: string;
        layerType: "grid" | "h3" | "line" | "geojson" | "point" | "arc" | "hexagon" | "cluster" | "heatmap" | "trip" | "s2";
        colorScale?: "custom" | "quantile" | "quantize" | "ordinal" | undefined;
        customColorScale?: number[] | undefined;
    }>, {
        success: boolean;
        layer?: string | undefined;
        details?: string | undefined;
        error?: string | undefined;
        instruction?: string | undefined;
    }, {
        layer: object;
        datasetId: string;
    } | undefined, {
        getDatasets: () => import("@kepler.gl/table").Datasets;
    }>;
    updateLayerColor: {
        context: {
            getLayers: () => import("@kepler.gl/layers/dist/base-layer").default[];
        };
        description: string;
        parameters: import("zod").ZodObject<{
            layerId: import("zod").ZodString;
            numberOfColors: import("zod").ZodNumber;
            customColors: import("zod").ZodArray<import("zod").ZodString, "many">;
        }, "strip", import("zod").ZodTypeAny, {
            layerId: string;
            numberOfColors: number;
            customColors: string[];
        }, {
            layerId: string;
            numberOfColors: number;
            customColors: string[];
        }>;
        execute: import("@openassistant/utils").ExecuteFunction<import("zod").ZodObject<{
            layerId: import("zod").ZodString;
            numberOfColors: import("zod").ZodNumber;
            customColors: import("zod").ZodArray<import("zod").ZodString, "many">;
        }, "strip", import("zod").ZodTypeAny, {
            layerId: string;
            numberOfColors: number;
            customColors: string[];
        }, {
            layerId: string;
            numberOfColors: number;
            customColors: string[];
        }>, {
            success: boolean;
            details?: string | undefined;
            error?: string | undefined;
            instruction?: string | undefined;
        }, {
            layerId: string;
            layer: import("@kepler.gl/layers/dist/base-layer").default;
            newConfig: Partial<import("@kepler.gl/types").LayerBaseConfig>;
            channel: string;
            newVisConfig: Partial<import("@kepler.gl/types").LayerVisConfig>;
        }, {
            getLayers: () => never;
            layerVisualChannelConfigChange: () => never;
        }>;
        component?: import("react").ElementType<any, keyof import("react").JSX.IntrinsicElements> | undefined;
        priority?: number | undefined;
    };
    loadData: import("@openassistant/utils").ExtendedTool<import("zod").ZodObject<{
        url: import("zod").ZodString;
    }, import("zod").UnknownKeysParam, import("zod").ZodTypeAny, {
        url: string;
    }, {
        url: string;
    }>, {
        success: boolean;
        url: string;
        details?: string | undefined;
        dataInfo?: object | undefined;
        instruction?: string | undefined;
    }, {
        parsedData: import("@kepler.gl/types").ProtoDataset[];
    } | undefined, {
        getLoaders: () => {
            loaders?: import("@loaders.gl/loader-utils").Loader<any, any, import("@loaders.gl/loader-utils").LoaderOptions>[] | undefined;
            loadOptions?: object | undefined;
        };
    }>;
    mapBoundary: {
        context: {
            getMapBoundary: () => {
                nw: [number, number];
                se: [number, number];
            } | undefined;
        };
        description: string;
        parameters: import("zod").ZodObject<{}, "strip", import("zod").ZodTypeAny, {}, {}>;
        execute: import("@openassistant/utils").ExecuteFunction<import("zod").ZodObject<{}, "strip", import("zod").ZodTypeAny, {}, {}>, {
            success: boolean;
            boundary: {
                nw: [number, number];
                se: [number, number];
            };
            error?: undefined;
            instruction?: undefined;
        }, never, {
            getMapBoundary: () => never;
        }>;
        component?: import("react").ElementType<any, keyof import("react").JSX.IntrinsicElements> | undefined;
        priority?: number | undefined;
    };
    saveDataToMap: import("@openassistant/utils").ExtendedTool<import("zod").ZodObject<{
        datasetNames: import("zod").ZodArray<import("zod").ZodString, "many">;
        saveDatasetName: import("zod").ZodString;
    }, "strip", import("zod").ZodTypeAny, {
        datasetNames: string[];
        saveDatasetName: string;
    }, {
        datasetNames: string[];
        saveDatasetName: string;
    }>, {
        success: boolean;
        savedDatasetName: string;
        details: string;
    }, {
        result: import("geojson").FeatureCollection<import("geojson").Geometry, import("geojson").GeoJsonProperties>[];
        loadedDatasetNames: string[];
        datasetId: string;
    }, never>;
};
