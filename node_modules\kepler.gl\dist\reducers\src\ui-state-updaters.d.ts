import { ExportImage } from '@kepler.gl/constants';
import { ActionTypes, KeplerGlInitPayload, LoadFilesErrUpdaterAction, UIStateActions } from '@kepler.gl/actions';
import { ExportData, ExportHtml, ExportJson, ExportMap, MapControls, UiState } from '@kepler.gl/types';
export declare const DEFAULT_ACTIVE_SIDE_PANEL = "layer";
export declare const DEFAULT_MODAL = "addData";
/**
 * A list of map control visibility and whether is it active.
 * @memberof uiStateUpdaters
 * @constant
 * @property visibleLayers Default: `{show: true, active: false}`
 * @property mapLegend Default: `{show: true, active: false}`
 * @property toggle3d Default: `{show: true}`
 * @property splitMap Default: `{show: true}`
 * @property mapDraw Default: `{show: true, active: false}`
 * @property mapLocale Default: `{show: false, active: false}`
 * @public
 */
export declare const DEFAULT_MAP_CONTROLS: MapControls;
/**
 * Default image export config
 * @memberof uiStateUpdaters
 * @constant
 * @property ratio Default: `'SCREEN'`,
 * @property resolution Default: `'ONE_X'`,
 * @property legend Default: `false`,
 * @property mapH Default: 0,
 * @property mapW Default: 0,
 * @property imageSize Default: {zoomOffset: 0, scale: 1, imageW: 0, imageH: 0},
 * @property imageDataUri Default: `''`,
 * @property exporting Default: `false`
 * @property error Default: `false`
 * @property escapeXhtmlForWebpack Default: `true`
 * @public
 */
export declare const DEFAULT_EXPORT_IMAGE: ExportImage;
export declare const DEFAULT_LOAD_FILES: {
    fileLoading: boolean;
};
/**
 * Default initial `exportData` settings
 * @memberof uiStateUpdaters
 * @constant
 * @property selectedDataset Default: `''`,
 * @property dataType Default: `'csv'`,
 * @property filtered Default: `true`,
 * @public
 */
export declare const DEFAULT_EXPORT_DATA: ExportData;
/**
 * @constant
 */
export declare const DEFAULT_NOTIFICATIONS: never[];
/**
 * @constant
 * @property exportMapboxAccessToken - Default: null, this is used when we provide a default mapbox token for users to take advantage of
 * @property userMapboxToken - Default: '', mapbox token provided by user through input field
 * @property mode - Default: 'READ', read only or editable
 * @public
 */
export declare const DEFAULT_EXPORT_HTML: ExportHtml;
/**
 * @constant
 * @property hasData - Default: 'true',
 * @public
 */
export declare const DEFAULT_EXPORT_JSON: ExportJson;
/**
 * Export Map Config
 * @constant
 * @property HTML - Default: 'DEFAULT_EXPORT_HTML',
 * @property JSON - Default: 'DEFAULT_EXPORT_JSON',
 * @property format - Default: 'HTML',
 * @public
 */
export declare const DEFAULT_EXPORT_MAP: ExportMap;
/**
 * Default initial `uiState`
 * @memberof uiStateUpdaters
 * @constant
 * @property readOnly Default: `false`
 * @property activeSidePanel Default: `'layer'`
 * @property currentModal Default: `'addData'`
 * @property datasetKeyToRemove Default: `null`
 * @property visibleDropdown Default: `null`
 * @property exportImage Default: [`DEFAULT_EXPORT_IMAGE`](#default_export_image)
 * @property exportData Default: [`DEFAULT_EXPORT_DATA`](#default_export_data)
 * @property exportMap Default: [`DEFAULT_EXPORT_MAP`](#default_export_map)
 * @property mapControls Default: [`DEFAULT_MAP_CONTROLS`](#default_map_controls)
 * @property notifications Default: `[]`
 * @property notifications Default: `[]`
 * @property loadFiles
 * @property isSidePanelCloseButtonVisible Default: `true`
 * @public
 */
export declare const INITIAL_UI_STATE: UiState;
/**
 * @memberof uiStateUpdaters
 */
export declare const initUiStateUpdater: (state: UiState, action: {
    type?: typeof ActionTypes['INIT'];
    payload: KeplerGlInitPayload;
}) => UiState;
/**
 * Toggle active side panel
 * @memberof uiStateUpdaters
 * @param state `uiState`
 * @param action
 * @param action.payload id of side panel to be shown, one of `layer`, `filter`, `interaction`, `map`. close side panel if `null`
 * @returns nextState
 * @public
 */
export declare const toggleSidePanelUpdater: (state: UiState, { payload: id }: UIStateActions.ToggleSidePanelUpdaterAction) => UiState;
/**
 * Show and hide modal dialog
 * @memberof uiStateUpdaters
 * @param state `uiState`
 * @param action
 * @paramaction.payload id of modal to be shown, null to hide modals. One of:
 *  - [`DATA_TABLE_ID`](../constants/default-settings.md#data_table_id)
 *  - [`DELETE_DATA_ID`](../constants/default-settings.md#delete_data_id)
 *  - [`ADD_DATA_ID`](../constants/default-settings.md#add_data_id)
 *  - [`EXPORT_IMAGE_ID`](../constants/default-settings.md#export_image_id)
 *  - [`EXPORT_DATA_ID`](../constants/default-settings.md#export_data_id)
 *  - [`ADD_MAP_STYLE_ID`](../constants/default-settings.md#add_map_style_id)
 * @returns nextState
 * @public
 */
export declare const toggleModalUpdater: (state: UiState, { payload: id }: UIStateActions.ToggleModalUpdaterAction) => UiState;
/**
 * Hide and show side panel header dropdown, activated by clicking the share link on top of the side panel
 * @memberof uiStateUpdaters
 * @public
 */
export declare const showExportDropdownUpdater: (state: UiState, { payload: id }: UIStateActions.ShowExportDropdownUpdaterAction) => UiState;
/**
 * Hide side panel header dropdown, activated by clicking the share link on top of the side panel
 * @memberof uiStateUpdaters
 * @public
 */
export declare const hideExportDropdownUpdater: (state: UiState) => UiState;
/**
 * Toggle side panel close button on top left of the side panel
 * @memberof uiStateUpdaters
 * @public
 */
export declare const toggleSidePanelCloseButtonUpdater: (state: UiState, { payload: { show } }: UIStateActions.ToggleSidePanelCloseButtonUpdaterAction) => UiState;
/**
 * Toggle active map control panel
 * @memberof uiStateUpdaters
 * @param state `uiState`
 * @param action action
 * @param action.payload map control panel id, one of the keys of: [`DEFAULT_MAP_CONTROLS`](#default_map_controls)
 * @returns nextState
 * @public
 */
export declare const toggleMapControlUpdater: (state: UiState, { payload: { panelId, index } }: UIStateActions.ToggleMapControlUpdaterAction) => UiState;
/**
 * Toggle map control visibility
 * @memberof uiStateUpdaters
 * @param state `uiState`
 * @param action action
 * @param action.payload map control panel id, one of the keys of: [`DEFAULT_MAP_CONTROLS`](#default_map_controls)
 * @returns nextState
 * @public
 */
export declare const setMapControlVisibilityUpdater: (state: UiState, { payload: { panelId, show } }: UIStateActions.setMapControlVisibilityUpdaterAction) => UiState;
/**
 * Toggle active map control panel
 * @memberof uiStateUpdaters
 * @param state `uiState`
 * @param action
 * @param action.payload dataset id
 * @returns nextState
 * @public
 */
export declare const openDeleteModalUpdater: (state: UiState, { payload: datasetKeyToRemove }: UIStateActions.OpenDeleteModalUpdaterAction) => UiState;
/**
 * Set `exportImage.legend` to `true` or `false`
 * @memberof uiStateUpdaters
 * @param state `uiState`
 * @returns nextState
 * @public
 */
export declare const setExportImageSettingUpdater: (state: UiState, { payload: newSetting }: UIStateActions.SetExportImageSettingUpdaterAction) => UiState;
/**
 * Set `exportImage.setExportImageDataUri` to a image dataUri
 * @memberof uiStateUpdaters
 * @param state `uiState`
 * @param action
 * @param action.payload export image data uri
 * @returns nextState
 * @public
 */
export declare const setExportImageDataUriUpdater: (state: UiState, { payload: dataUri }: UIStateActions.SetExportImageDataUriUpdaterAction) => UiState;
/**
 * @memberof uiStateUpdaters
 * @public
 */
export declare const setExportImageErrorUpdater: (state: UiState, { payload: error }: UIStateActions.SetExportImageErrorUpdaterAction) => UiState;
/**
 * Delete cached export image
 * @memberof uiStateUpdaters
 * @public
 */
export declare const cleanupExportImageUpdater: (state: UiState) => UiState;
/**
 * Start image exporting flow
 * @memberof uiStateUpdaters
 * @param state
 * @param options
 * @returns {UiState}
 * @public
 */
export declare const startExportingImageUpdater: (state: UiState, { payload: options }: {
    payload: Partial<ExportImage>;
}) => UiState;
/**
 * Set selected dataset for export
 * @memberof uiStateUpdaters
 * @param state `uiState`
 * @param action
 * @param action.payload dataset id
 * @returns nextState
 * @public
 */
export declare const setExportSelectedDatasetUpdater: (state: UiState, { payload: dataset }: UIStateActions.SetExportSelectedDatasetUpdaterAction) => UiState;
/**
 * Set data format for exporting data
 * @memberof uiStateUpdaters
 * @param state `uiState`
 * @param action
 * @param action.payload one of `'text/csv'`
 * @returns nextState
 * @public
 */
export declare const setExportDataTypeUpdater: (state: UiState, { payload: dataType }: UIStateActions.SetExportDataTypeUpdaterAction) => UiState;
/**
 * Whether to export filtered data, `true` or `false`
 * @memberof uiStateUpdaters
 * @param state `uiState`
 * @param action
 * @param action.payload
 * @returns nextState
 * @public
 */
export declare const setExportFilteredUpdater: (state: UiState, { payload: filtered }: UIStateActions.SetExportFilteredUpdaterAction) => UiState;
/**
 * Whether to including data in map config, toggle between `true` or `false`
 * @memberof uiStateUpdaters
 * @param state `uiState`
 * @returns nextState
 * @public
 */
export declare const setExportDataUpdater: (state: UiState) => UiState;
/**
 * whether to export a mapbox access to HTML single page
 * @param state - `uiState`
 * @param action
 * @param action.payload
 * @returns nextState
 * @public
 */
export declare const setUserMapboxAccessTokenUpdater: (state: UiState, { payload: userMapboxToken }: UIStateActions.SetUserMapboxAccessTokenUpdaterAction) => UiState;
/**
 * Sets the export map format
 * @param state - `uiState`
 * @param action
 * @param action.payload format to use to export the map into
 * @return nextState
 */
export declare const setExportMapFormatUpdater: (state: UiState, { payload: format }: UIStateActions.SetExportMapFormatUpdaterAction) => UiState;
/**
 * Set the export html map mode
 * @param state - `uiState`
 * @param action
 * @param action.payload to be set (available modes: EXPORT_HTML_MAP_MODES)
 * @return nextState
 */
export declare const setExportMapHTMLModeUpdater: (state: UiState, { payload: mode }: UIStateActions.SetExportHTMLMapModeUpdaterAction) => UiState;
/**
 * Adds a new notification.
 * Updates a notification in case of matching ids.
 * @memberof uiStateUpdaters
 * @param state `uiState`
 * @param action
 * @param action.payload Params of a notification
 * @returns nextState
 * @public
 */
export declare const addNotificationUpdater: (state: UiState, { payload }: UIStateActions.AddNotificationUpdaterAction) => UiState;
/**
 * Remove a notification
 * @memberof uiStateUpdaters
 * @param state `uiState`
 * @param action
 * @param action.payload id of the notification to be removed
 * @returns nextState
 * @public
 */
export declare const removeNotificationUpdater: (state: UiState, { payload: id }: UIStateActions.RemoveNotificationUpdaterAction) => UiState;
/**
 * Fired when file loading begin
 * @memberof uiStateUpdaters
 * @param state `uiState`
 * @returns nextState
 * @public
 */
export declare const loadFilesUpdater: (state: UiState) => UiState;
/**
 * Handles loading file success and set fileLoading property to false
 * @memberof uiStateUpdaters
 * @param state `uiState`
 * @returns nextState
 */
export declare const loadFilesSuccessUpdater: (state: UiState) => UiState;
/**
 * Handles load file error and set fileLoading property to false
 * @memberof uiStateUpdaters
 * @param state
 * @param action
 * @param action.error
 * @returns nextState
 * @public
 */
export declare const loadFilesErrUpdater: (state: UiState, { error }: LoadFilesErrUpdaterAction) => UiState;
/**
 * Handles toggle map split and reset all map control index to 0
 * @memberof uiStateUpdaters
 * @param state
 * @returns nextState
 * @public
 */
export declare const toggleSplitMapUpdater: (state: UiState) => UiState;
/**
 * Toggle modal data
 * @memberof uiStateUpdaters
 * @param state
 * @returns nextState
 * @public
 */
export declare const showDatasetTableUpdater: (state: UiState) => UiState;
/**
 * Set the locale of the UI
 * @memberof uiStateUpdaters
 * @param state `uiState`
 * @param action
 * @param action.payload
 * @param action.payload.locale locale
 * @returns nextState
 * @public
 */
export declare const setLocaleUpdater: (state: UiState, { payload: { locale } }: UIStateActions.SetLocaleUpdaterAction) => UiState;
/**
 * Toggle layer panel list view
 * @memberof uiStateUpdaters
 * @param state `uiState`
 * @param action
 * @param action.payload layer panel listView value. Can be 'list' or 'sortByDataset'
 * @returns nextState
 * @public
 */
export declare const togglePanelListViewUpdater: (state: UiState, { payload: { panelId, listView } }: UIStateActions.TogglePanelListViewAction) => UiState;
