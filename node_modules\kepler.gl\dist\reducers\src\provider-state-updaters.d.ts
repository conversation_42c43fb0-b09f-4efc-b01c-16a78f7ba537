import { ProviderActions } from '@kepler.gl/actions';
import { ExportFileToCloudPayload } from '@kepler.gl/types';
import { MapListItem } from '@kepler.gl/cloud-providers';
declare type ActionPayload<P> = {
    type?: string;
    payload: P;
};
export declare type ProviderState = {
    isProviderLoading: boolean;
    isCloudMapLoading: boolean;
    providerError: any;
    currentProvider: string | null;
    successInfo: any;
    mapSaved: null | string;
    initialState?: any;
    visualizations: MapListItem[];
};
export declare const INITIAL_PROVIDER_STATE: ProviderState;
/**
 * This method will export the current kepler config file to the chosen cloud proder
 * add returns a share URL
 *
 */
export declare const exportFileToCloudUpdater: (state: ProviderState, action: ActionPayload<ExportFileToCloudPayload>) => ProviderState;
export declare const exportFileSuccessUpdater: (state: ProviderState, action: ActionPayload<ProviderActions.ExportFileSuccessPayload>) => ProviderState;
/**
 * Close modal on success and display notification
 */
export declare const postSaveLoadSuccessUpdater: (state: ProviderState, action: ActionPayload<ProviderActions.PostSaveLoadSuccessPayload>) => ProviderState;
export declare const exportFileErrorUpdater: (state: ProviderState, action: ActionPayload<ProviderActions.ExportFileErrorPayload>) => ProviderState;
export declare const loadCloudMapUpdater: (state: ProviderState, action: ActionPayload<ProviderActions.LoadCloudMapPayload>) => ProviderState;
export declare const loadCloudMapSuccessUpdater: (state: ProviderState, action: ActionPayload<ProviderActions.LoadCloudMapSuccessPayload>) => ProviderState;
export declare const loadCloudMapErrorUpdater: (state: ProviderState, action: ActionPayload<ProviderActions.LoadCloudMapErrorPayload>) => ProviderState;
export declare const resetProviderStatusUpdater: (state: ProviderState) => ProviderState;
export {};
