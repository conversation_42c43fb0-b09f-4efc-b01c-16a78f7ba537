export declare const visStateReducerFactory: (initialState?: {}) => import("redux-actions").ReduxCompatibleReducer<{
    filters: import("@kepler.gl/types").Filter[];
    mapInfo: import("@kepler.gl/types").MapInfo;
    layers: import("@kepler.gl/layers/dist/base-layer").default[];
    layerData: any[];
    layerToBeMerged: any[];
    layerOrder: string[];
    effects: import("@kepler.gl/types").Effect[];
    effectOrder: string[];
    filterToBeMerged: any[];
    datasets: import("@kepler.gl/table").Datasets;
    editingDataset: string | undefined;
    interactionConfig: import("@kepler.gl/types").InteractionConfig;
    interactionToBeMerged: any;
    layerBlending: string;
    overlayBlending?: string | undefined;
    hoverInfo: any;
    clicked: any;
    mousePos: any;
    maxDefaultTooltips: number;
    layerClasses: {
        point: typeof import("@kepler.gl/layers/dist/point-layer/point-layer").default;
        arc: typeof import("@kepler.gl/layers/dist/arc-layer/arc-layer").default;
        line: typeof import("@kepler.gl/layers/dist/line-layer/line-layer").default;
        grid: typeof import("@kepler.gl/layers/dist/grid-layer/grid-layer").default;
        hexagon: typeof import("@kepler.gl/layers/dist/hexagon-layer/hexagon-layer").default;
        geojson: typeof import("@kepler.gl/layers/dist/geojson-layer/geojson-layer").default;
        cluster: typeof import("@kepler.gl/layers/dist/cluster-layer/cluster-layer").default;
        icon: typeof import("@kepler.gl/layers/dist/icon-layer/icon-layer").default;
        heatmap: typeof import("@kepler.gl/layers/dist/heatmap-layer/heatmap-layer").default;
        hexagonId: typeof import("@kepler.gl/layers/dist/h3-hexagon-layer/h3-hexagon-layer").default;
        "3D": typeof import("@kepler.gl/layers").ScenegraphLayer;
        trip: typeof import("@kepler.gl/layers/dist/trip-layer/trip-layer").default;
        s2: typeof import("@kepler.gl/layers/dist/s2-geometry-layer/s2-geometry-layer").default;
    };
    animationConfig: import("@kepler.gl/types").AnimationConfig;
    editor: import("@kepler.gl/types").Editor;
    splitMaps: import("@kepler.gl/types").SplitMap[];
    splitMapsToBeMerged: import("@kepler.gl/types").SplitMap[];
    fileLoading: false | import("@kepler.gl/types").FileLoading;
    fileLoadingProgress: import("@kepler.gl/types").FileLoadingProgress;
    loaders: import("@loaders.gl/loader-utils").Loader<any, any, import("@loaders.gl/loader-utils").LoaderOptions>[];
    loadOptions: object;
    initialState?: Partial<import("@kepler.gl/schemas").VisState> | undefined;
    mergers: import("@kepler.gl/schemas").VisStateMergers<any>;
    schema: import("@kepler.gl/schemas").KeplerGLSchemaClass;
    preserveLayerOrder?: string[] | undefined;
    preserveFilterOrder?: string[] | undefined;
    preserveDatasetOrder?: string[] | undefined;
    isMergingDatasets: {
        [datasetId: string]: boolean;
    };
}, {
    filters: import("@kepler.gl/types").Filter[];
    mapInfo: import("@kepler.gl/types").MapInfo;
    layers: import("@kepler.gl/layers/dist/base-layer").default[];
    layerData: any[];
    layerToBeMerged: any[];
    layerOrder: string[];
    effects: import("@kepler.gl/types").Effect[];
    effectOrder: string[];
    filterToBeMerged: any[];
    datasets: import("@kepler.gl/table").Datasets;
    editingDataset: string | undefined;
    interactionConfig: import("@kepler.gl/types").InteractionConfig;
    interactionToBeMerged: any;
    layerBlending: string;
    overlayBlending?: string | undefined;
    hoverInfo: any;
    clicked: any;
    mousePos: any;
    maxDefaultTooltips: number;
    layerClasses: {
        point: typeof import("@kepler.gl/layers/dist/point-layer/point-layer").default;
        arc: typeof import("@kepler.gl/layers/dist/arc-layer/arc-layer").default;
        line: typeof import("@kepler.gl/layers/dist/line-layer/line-layer").default;
        grid: typeof import("@kepler.gl/layers/dist/grid-layer/grid-layer").default;
        hexagon: typeof import("@kepler.gl/layers/dist/hexagon-layer/hexagon-layer").default;
        geojson: typeof import("@kepler.gl/layers/dist/geojson-layer/geojson-layer").default;
        cluster: typeof import("@kepler.gl/layers/dist/cluster-layer/cluster-layer").default;
        icon: typeof import("@kepler.gl/layers/dist/icon-layer/icon-layer").default;
        heatmap: typeof import("@kepler.gl/layers/dist/heatmap-layer/heatmap-layer").default;
        hexagonId: typeof import("@kepler.gl/layers/dist/h3-hexagon-layer/h3-hexagon-layer").default;
        "3D": typeof import("@kepler.gl/layers").ScenegraphLayer;
        trip: typeof import("@kepler.gl/layers/dist/trip-layer/trip-layer").default;
        s2: typeof import("@kepler.gl/layers/dist/s2-geometry-layer/s2-geometry-layer").default;
    };
    animationConfig: import("@kepler.gl/types").AnimationConfig;
    editor: import("@kepler.gl/types").Editor;
    splitMaps: import("@kepler.gl/types").SplitMap[];
    splitMapsToBeMerged: import("@kepler.gl/types").SplitMap[];
    fileLoading: false | import("@kepler.gl/types").FileLoading;
    fileLoadingProgress: import("@kepler.gl/types").FileLoadingProgress;
    loaders: import("@loaders.gl/loader-utils").Loader<any, any, import("@loaders.gl/loader-utils").LoaderOptions>[];
    loadOptions: object;
    initialState?: Partial<import("@kepler.gl/schemas").VisState> | undefined;
    mergers: import("@kepler.gl/schemas").VisStateMergers<any>;
    schema: import("@kepler.gl/schemas").KeplerGLSchemaClass;
    preserveLayerOrder?: string[] | undefined;
    preserveFilterOrder?: string[] | undefined;
    preserveDatasetOrder?: string[] | undefined;
    isMergingDatasets: {
        [datasetId: string]: boolean;
    };
}>;
declare const _default: import("redux-actions").ReduxCompatibleReducer<{
    filters: import("@kepler.gl/types").Filter[];
    mapInfo: import("@kepler.gl/types").MapInfo;
    layers: import("@kepler.gl/layers/dist/base-layer").default[];
    layerData: any[];
    layerToBeMerged: any[];
    layerOrder: string[];
    effects: import("@kepler.gl/types").Effect[];
    effectOrder: string[];
    filterToBeMerged: any[];
    datasets: import("@kepler.gl/table").Datasets;
    editingDataset: string | undefined;
    interactionConfig: import("@kepler.gl/types").InteractionConfig;
    interactionToBeMerged: any;
    layerBlending: string;
    overlayBlending?: string | undefined;
    hoverInfo: any;
    clicked: any;
    mousePos: any;
    maxDefaultTooltips: number;
    layerClasses: {
        point: typeof import("@kepler.gl/layers/dist/point-layer/point-layer").default;
        arc: typeof import("@kepler.gl/layers/dist/arc-layer/arc-layer").default;
        line: typeof import("@kepler.gl/layers/dist/line-layer/line-layer").default;
        grid: typeof import("@kepler.gl/layers/dist/grid-layer/grid-layer").default;
        hexagon: typeof import("@kepler.gl/layers/dist/hexagon-layer/hexagon-layer").default;
        geojson: typeof import("@kepler.gl/layers/dist/geojson-layer/geojson-layer").default;
        cluster: typeof import("@kepler.gl/layers/dist/cluster-layer/cluster-layer").default;
        icon: typeof import("@kepler.gl/layers/dist/icon-layer/icon-layer").default;
        heatmap: typeof import("@kepler.gl/layers/dist/heatmap-layer/heatmap-layer").default;
        hexagonId: typeof import("@kepler.gl/layers/dist/h3-hexagon-layer/h3-hexagon-layer").default;
        "3D": typeof import("@kepler.gl/layers").ScenegraphLayer;
        trip: typeof import("@kepler.gl/layers/dist/trip-layer/trip-layer").default;
        s2: typeof import("@kepler.gl/layers/dist/s2-geometry-layer/s2-geometry-layer").default;
    };
    animationConfig: import("@kepler.gl/types").AnimationConfig;
    editor: import("@kepler.gl/types").Editor;
    splitMaps: import("@kepler.gl/types").SplitMap[];
    splitMapsToBeMerged: import("@kepler.gl/types").SplitMap[];
    fileLoading: false | import("@kepler.gl/types").FileLoading;
    fileLoadingProgress: import("@kepler.gl/types").FileLoadingProgress;
    loaders: import("@loaders.gl/loader-utils").Loader<any, any, import("@loaders.gl/loader-utils").LoaderOptions>[];
    loadOptions: object;
    initialState?: Partial<import("@kepler.gl/schemas").VisState> | undefined;
    mergers: import("@kepler.gl/schemas").VisStateMergers<any>;
    schema: import("@kepler.gl/schemas").KeplerGLSchemaClass;
    preserveLayerOrder?: string[] | undefined;
    preserveFilterOrder?: string[] | undefined;
    preserveDatasetOrder?: string[] | undefined;
    isMergingDatasets: {
        [datasetId: string]: boolean;
    };
}, {
    filters: import("@kepler.gl/types").Filter[];
    mapInfo: import("@kepler.gl/types").MapInfo;
    layers: import("@kepler.gl/layers/dist/base-layer").default[];
    layerData: any[];
    layerToBeMerged: any[];
    layerOrder: string[];
    effects: import("@kepler.gl/types").Effect[];
    effectOrder: string[];
    filterToBeMerged: any[];
    datasets: import("@kepler.gl/table").Datasets;
    editingDataset: string | undefined;
    interactionConfig: import("@kepler.gl/types").InteractionConfig;
    interactionToBeMerged: any;
    layerBlending: string;
    overlayBlending?: string | undefined;
    hoverInfo: any;
    clicked: any;
    mousePos: any;
    maxDefaultTooltips: number;
    layerClasses: {
        point: typeof import("@kepler.gl/layers/dist/point-layer/point-layer").default;
        arc: typeof import("@kepler.gl/layers/dist/arc-layer/arc-layer").default;
        line: typeof import("@kepler.gl/layers/dist/line-layer/line-layer").default;
        grid: typeof import("@kepler.gl/layers/dist/grid-layer/grid-layer").default;
        hexagon: typeof import("@kepler.gl/layers/dist/hexagon-layer/hexagon-layer").default;
        geojson: typeof import("@kepler.gl/layers/dist/geojson-layer/geojson-layer").default;
        cluster: typeof import("@kepler.gl/layers/dist/cluster-layer/cluster-layer").default;
        icon: typeof import("@kepler.gl/layers/dist/icon-layer/icon-layer").default;
        heatmap: typeof import("@kepler.gl/layers/dist/heatmap-layer/heatmap-layer").default;
        hexagonId: typeof import("@kepler.gl/layers/dist/h3-hexagon-layer/h3-hexagon-layer").default;
        "3D": typeof import("@kepler.gl/layers").ScenegraphLayer;
        trip: typeof import("@kepler.gl/layers/dist/trip-layer/trip-layer").default;
        s2: typeof import("@kepler.gl/layers/dist/s2-geometry-layer/s2-geometry-layer").default;
    };
    animationConfig: import("@kepler.gl/types").AnimationConfig;
    editor: import("@kepler.gl/types").Editor;
    splitMaps: import("@kepler.gl/types").SplitMap[];
    splitMapsToBeMerged: import("@kepler.gl/types").SplitMap[];
    fileLoading: false | import("@kepler.gl/types").FileLoading;
    fileLoadingProgress: import("@kepler.gl/types").FileLoadingProgress;
    loaders: import("@loaders.gl/loader-utils").Loader<any, any, import("@loaders.gl/loader-utils").LoaderOptions>[];
    loadOptions: object;
    initialState?: Partial<import("@kepler.gl/schemas").VisState> | undefined;
    mergers: import("@kepler.gl/schemas").VisStateMergers<any>;
    schema: import("@kepler.gl/schemas").KeplerGLSchemaClass;
    preserveLayerOrder?: string[] | undefined;
    preserveFilterOrder?: string[] | undefined;
    preserveDatasetOrder?: string[] | undefined;
    isMergingDatasets: {
        [datasetId: string]: boolean;
    };
}>;
export default _default;
