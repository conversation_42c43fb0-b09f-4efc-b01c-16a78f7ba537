{"name": "ktx-parse", "version": "0.0.4", "description": "KTX 2.0 (.ktx2) parser and serializer.", "source": "src/index.ts", "main": "dist/ktx-parse.js", "module": "dist/ktx-parse.modern.js", "types": "dist/index.d.ts", "repository": "github:donmccurdy/ktx-parse", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "scripts": {"dist": "microbundle --format modern,cjs --define PACKAGE_VERSION=$npm_package_version", "watch": "microbundle watch --format modern,cjs --define PACKAGE_VERSION=$npm_package_version", "watch:debug": "microbundle watch --no-compress --format modern,cjs --define PACKAGE_VERSION=$npm_package_version", "test": "ts-node node_modules/tape/bin/tape test/test.ts | tap-spec", "coverage": "nyc --reporter=lcov --reporter=text ts-node node_modules/tape/bin/tape test/test.ts", "coverage:report": "nyc report --reporter=text-lcov | coveralls", "docs": "typedoc --plugin typedoc-plugin-markdown --out ./docs --hideProjectName --hideBreadcrumbs && ts-node docs.ts && rm -rf ./docs", "preversion": "yarn dist && yarn test", "version": "yarn dist && yarn docs && git add -u", "postversion": "git push && git push --tags && npm publish && yarn coverage:report"}, "devDependencies": {"@types/tape": "^4.13.0", "@typescript-eslint/eslint-plugin": "^4.10.0", "coveralls": "^3.1.0", "eslint": "^7.15.0", "microbundle": "^0.12.4", "nyc": "^15.1.0", "source-map-support": "^0.5.19", "tap-spec": "^5.0.0", "tape": "^5.0.1", "ts-node": "^9.1.1", "typedoc": "^0.19.2", "typedoc-plugin-markdown": "^3.1.1", "typescript": "^4.1.3"}, "files": ["dist/", "src/", "README.md", "LICENSE", "package.json"]}