"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.propertiesV1 = exports.propertiesV0 = exports["default"] = exports.customMapStylePropsV1 = exports.MapStyleSchemaV1 = exports.CustomMapStyleSchema = void 0;
var _typeof2 = _interopRequireDefault(require("@babel/runtime/helpers/typeof"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var _versions = require("./versions");
var _schema = _interopRequireDefault(require("./schema"));
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2["default"])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2["default"])(o), (0, _possibleConstructorReturn2["default"])(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2["default"])(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); } // SPDX-License-Identifier: MIT
// Copyright contributors to the kepler.gl project
var customMapStylePropsV1 = exports.customMapStylePropsV1 = {
  accessToken: null,
  custom: null,
  icon: null,
  id: null,
  label: null,
  url: null
};
var CustomMapStyleSchema = exports.CustomMapStyleSchema = new _schema["default"]({
  version: _versions.VERSIONS.v1,
  key: 'customStyle',
  properties: customMapStylePropsV1
});
var MapStyleSchemaV1 = exports.MapStyleSchemaV1 = /*#__PURE__*/function (_Schema) {
  function MapStyleSchemaV1() {
    var _this;
    (0, _classCallCheck2["default"])(this, MapStyleSchemaV1);
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    _this = _callSuper(this, MapStyleSchemaV1, [].concat(args));
    (0, _defineProperty2["default"])(_this, "version", _versions.VERSIONS.v1);
    (0, _defineProperty2["default"])(_this, "key", 'mapStyles');
    return _this;
  }
  (0, _inherits2["default"])(MapStyleSchemaV1, _Schema);
  return (0, _createClass2["default"])(MapStyleSchemaV1, [{
    key: "save",
    value: function save(mapStyles) {
      // save all custom styles
      var saveCustomStyle = Object.keys(mapStyles).reduce(function (accu, key) {
        return _objectSpread(_objectSpread({}, accu), mapStyles[key].custom ? (0, _defineProperty2["default"])({}, key, CustomMapStyleSchema.save(mapStyles[key]).customStyle) : {});
      }, {});
      return (0, _defineProperty2["default"])({}, this.key, saveCustomStyle);
    }
  }, {
    key: "load",
    value: function load(mapStyles) {
      // If mapStyle is an empty object, do not load it
      return (0, _typeof2["default"])(mapStyles) === 'object' && Object.keys(mapStyles).length ? (0, _defineProperty2["default"])({}, this.key, mapStyles) : {};
    }
  }]);
}(_schema["default"]); // version v0
var propertiesV0 = exports.propertiesV0 = {
  styleType: null,
  topLayerGroups: null,
  visibleLayerGroups: null,
  buildingLayer: null,
  mapStyles: new MapStyleSchemaV1()
};
var propertiesV1 = exports.propertiesV1 = {
  styleType: null,
  topLayerGroups: null,
  visibleLayerGroups: null,
  threeDBuildingColor: null,
  backgroundColor: null,
  mapStyles: new MapStyleSchemaV1()
};
var mapStyleSchema = (0, _defineProperty2["default"])((0, _defineProperty2["default"])({}, _versions.VERSIONS.v0, new _schema["default"]({
  version: _versions.VERSIONS.v0,
  properties: propertiesV0,
  key: 'mapStyle'
})), _versions.VERSIONS.v1, new _schema["default"]({
  version: _versions.VERSIONS.v1,
  properties: propertiesV1,
  key: 'mapStyle'
}));
var _default = exports["default"] = mapStyleSchema;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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