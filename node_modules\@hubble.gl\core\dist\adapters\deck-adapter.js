// hubble.gl
// SPDX-License-Identifier: MIT
// Copyright (c) vis.gl contributors
/* eslint-disable no-console */
import { PreviewEncoder } from "../encoders/index.js";
import { AnimationManager } from "../animations/index.js";
import { VideoCapture } from "../capture/video-capture.js";
export default class DeckAdapter {
    constructor({ animationManager = undefined, glContext = undefined }) {
        this.animationManager = animationManager || new AnimationManager({});
        this.glContext = glContext;
        this.videoCapture = new VideoCapture();
        this.shouldAnimate = false;
        this.enabled = false;
        this.getProps = this.getProps.bind(this);
        this.render = this.render.bind(this);
        this.stop = this.stop.bind(this);
        this.seek = this.seek.bind(this);
    }
    setDeck(deck) {
        this.deck = deck;
    }
    getProps({ deck, onNextFrame = undefined, extraProps = undefined }) {
        if (deck) {
            this.deck = deck;
        }
        const props = {
            _animate: this.shouldAnimate
        };
        if (onNextFrame) {
            props.onAfterRender = () => this.onAfterRender(onNextFrame);
        }
        if (this.enabled) {
            props.controller = false;
        }
        else {
            props.controller = true;
        }
        if (this.glContext) {
            props.gl = this.glContext;
        }
        return { ...extraProps, ...props };
    }
    render({ Encoder = PreviewEncoder, formatConfigs = {}, filename = undefined, timecode = { start: 0, end: 0, framerate: 30 }, onStopped = undefined, onSave = undefined, onComplete = undefined }) {
        this.shouldAnimate = true;
        this.videoCapture.render({
            Encoder,
            formatConfigs,
            timecode,
            filename,
            onStop: () => this.stop({ onStopped, onSave, onComplete })
        });
        this.enabled = true;
        this.seek({ timeMs: timecode.start });
    }
    stop({ onStopped, onSave, onComplete, abort }) {
        this.enabled = false;
        this.shouldAnimate = false;
        this.videoCapture.stop({ onStopped, onSave, onComplete, abort });
    }
    seek({ timeMs }) {
        this.animationManager.timeline.setTime(timeMs);
        this.animationManager.draw();
    }
    onAfterRender(proceedToNextFrame, readyToCapture = true) {
        const areAllLayersLoaded = this.deck && this.deck.props.layers.every(layer => layer.isLoaded);
        if (this.videoCapture.isRecording() && areAllLayersLoaded && readyToCapture) {
            // @ts-expect-error TODO use getCanvas
            const canvas = this.deck.canvas;
            this.videoCapture.capture(canvas, nextTimeMs => {
                this.seek({ timeMs: nextTimeMs });
                proceedToNextFrame(nextTimeMs);
            });
        }
    }
}
