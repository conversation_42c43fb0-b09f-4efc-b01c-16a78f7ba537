{"name": "@kepler.gl/processors", "author": "<PERSON> <<EMAIL>>", "version": "3.1.8", "description": "kepler.gl constants used by kepler.gl components, actions and reducers", "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "keywords": ["babel", "es6", "react", "webgl", "visualization", "deck.gl"], "repository": {"type": "git", "url": "https://github.com/keplergl/kepler.gl.git"}, "scripts": {"build": "rm -fr dist && babel src --out-dir dist --source-maps inline --extensions '.ts,.tsx,.js,.jsx' --ignore '**/*.d.ts'", "build:umd": "NODE_OPTIONS=--openssl-legacy-provider webpack --config ./webpack/umd.js --progress --env.prod", "build:types": "tsc --project ./tsconfig.production.json", "prepublishOnly": "babel-node ../../scripts/license-header/bin --license ../../FILE-HEADER && yarn build && yarn build:types", "stab": "mkdir -p dist && touch dist/index.js"}, "files": ["dist", "umd"], "dependencies": {"@danmarshall/deckgl-typings": "4.9.22", "@kepler.gl/common-utils": "3.1.8", "@kepler.gl/constants": "3.1.8", "@kepler.gl/schemas": "3.1.8", "@kepler.gl/table": "3.1.8", "@kepler.gl/types": "3.1.8", "@kepler.gl/utils": "3.1.8", "@loaders.gl/arrow": "^4.3.2", "@loaders.gl/core": "^4.3.2", "@loaders.gl/csv": "^4.3.2", "@loaders.gl/gis": "^4.3.2", "@loaders.gl/json": "^4.3.2", "@loaders.gl/loader-utils": "^4.3.2", "@loaders.gl/parquet": "^4.3.2", "@loaders.gl/schema": "^4.3.2", "@loaders.gl/wkt": "^4.3.2", "@mapbox/geojson-normalize": "0.0.1", "@nebula.gl/edit-modes": "1.0.2-alpha.1", "@turf/helpers": "^6.1.4", "apache-arrow": ">=15.0.0", "d3-dsv": "^2.0.0", "type-analyzer": "0.4.0"}, "nyc": {"sourceMap": false, "instrument": false}, "maintainers": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "engines": {"node": ">=18"}, "volta": {"node": "18.18.2", "yarn": "4.4.0"}, "packageManager": "yarn@4.4.0"}