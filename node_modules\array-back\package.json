{"name": "array-back", "author": "<PERSON> <<EMAIL>>", "version": "6.2.2", "description": "Guarantees an array back", "repository": "https://github.com/75lb/array-back.git", "license": "MIT", "exports": {"import": "./index.js", "require": "./dist/index.cjs"}, "type": "module", "keywords": ["to", "convert", "return", "array", "arrayify"], "engines": {"node": ">=12.17"}, "files": ["index.js", "index.mjs", "dist"], "scripts": {"test": "npm run dist && npm run test:node", "test:node": "test-runner test.js", "test:web": "web-runner test.js", "docs": "jsdoc2md -t README.hbs index.js > README.md", "dist": "rollup -c"}, "devDependencies": {"isomorphic-assert": "^1.0.0", "jsdoc-to-markdown": "^7.1.0", "rollup": "^2.64.0", "test-object-model": "^0.7.1", "test-runner": "^0.10.0"}}