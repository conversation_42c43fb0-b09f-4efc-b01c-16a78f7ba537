// geojson.js - v0.5.0
// (c) 2016 <PERSON>, MIT License 
!function(a){function b(){var a=1<=arguments.length?[].slice.call(arguments,0):[],b=a.shift(),c=a.shift();Error.apply(this,a),this.message=this.message||"Invalid Geometry: item: "+JSON.stringify(b)+", params: "+JSON.stringify(c)}function c(a,b){var c=a||{};for(var d in b)b.hasOwnProperty(d)&&!c[d]&&(c[d]=b[d]);return c}function d(a,b){if(b.crs&&e(b.crs)&&(b.isPostgres?a.geometry.crs=b.crs:a.crs=b.crs),b.bbox&&(a.bbox=b.bbox),b.extraGlobal){a.properties={};for(var c in b.extraGlobal)a.properties[c]=b.extraGlobal[c]}}function e(a){if("name"===a.type){if(a.properties&&a.properties.name)return!0;throw new Error('Invalid CRS. Properties must contain "name" key')}if("link"===a.type){if(a.properties&&a.properties.href&&a.properties.type)return!0;throw new Error('Invalid CRS. Properties must contain "href" and "type" key')}throw new Error('Invald CRS. Type attribute must be "name" or "link"')}function f(a){a.geom={};for(var b in a)a.hasOwnProperty(b)&&-1!==m.indexOf(b)&&(a.geom[b]=a[b],delete a[b]);g(a.geom)}function g(a){for(var b in a)a.hasOwnProperty(b)&&("string"==typeof a[b]?n.push(a[b]):"object"==typeof a[b]&&(n.push(a[b][0]),n.push(a[b][1])));if(0===n.length)throw new Error("No geometry attributes specified")}function h(a){var b=a.item,c=a.params,d=a.propFunc,e={type:"Feature"};return e.geometry=j(b,c),e.properties=d.call(b),e}function i(a){return/^.+\..+$/.test(a)}function j(c,d){var e={};for(var f in d.geom){var g=d.geom[f];if("string"==typeof g&&c.hasOwnProperty(g))"GeoJSON"===f?e=c[g]:(e.type=f,e.coordinates=c[g]);else if("object"!=typeof g||Array.isArray(g)){if(Array.isArray(g)&&c.hasOwnProperty(g[0])&&c.hasOwnProperty(g[1]))e.type=f,e.coordinates=[Number(c[g[1]]),Number(c[g[0]])];else if(Array.isArray(g)&&i(g[0])&&i(g[1])){for(var h=[],k=0;k<g.length;k++){for(var l=g[k].split("."),m=c,n=0;n<l.length;n++){if(!m.hasOwnProperty(l[n]))return!1;m=m[l[n]]}h[k]=m}e.type=f,e.coordinates=[Number(h[1]),Number(h[0])]}}else{var o=Object.keys(g).map(function(a){var b=g[a],d=c[a];return j(d,{geom:{Point:b}})});e.type=f,e.coordinates=[].concat(o.map(function(a){return a.coordinates}))}}if(d.doThrows&&d.doThrows.invalidGeometry&&!a.isGeometryValid(e))throw new b(c,d);return e}function k(a){var b;return a.exclude||a.include?a.include?b=function(b){a.include.forEach(function(a){b[a]=this[a]},this)}:a.exclude&&(b=function(b){for(var c in this)this.hasOwnProperty(c)&&-1===n.indexOf(c)&&-1===a.exclude.indexOf(c)&&(b[c]=this[c])}):b=function(a){for(var b in this)this.hasOwnProperty(b)&&-1===n.indexOf(b)&&(a[b]=this[b])},function(){var c={};return b.call(this,c),a.extra&&l(c,a.extra),c}}function l(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c]);return a}a.version="0.5.0",a.defaults={doThrows:{invalidGeometry:!1}},b.prototype=Error,a.errors={InvalidGeometryError:b},a.isGeometryValid=function(a){return a&&Object.keys(a).length?!!a.type&&!!a.coordinates&&Array.isArray(a.coordinates)&&!!a.coordinates.length:!1},a.parse=function(a,b,e){var g,i,j=c(b,this.defaults);return n.length=0,f(j),i=k(j),Array.isArray(a)?(g={type:"FeatureCollection",features:[]},a.forEach(function(a){g.features.push(h({item:a,params:j,propFunc:i}))}),d(g,j)):(g=h({item:a,params:j,propFunc:i}),d(g,j)),e&&"function"==typeof e?void e(g):g};var m=["Point","MultiPoint","LineString","MultiLineString","Polygon","MultiPolygon","GeoJSON"],n=[]}("object"==typeof module?module.exports:window.GeoJSON={});