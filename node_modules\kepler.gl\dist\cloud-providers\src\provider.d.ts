import { MapData, ExportFileOptions, Millisecond, SavedMap } from '@kepler.gl/types';
import { ComponentType } from 'react';
export declare type MapItemLoadParams = {
    id: string;
    path: string;
};
export declare type MapListItem = {
    id: string;
    title: string;
    description: string;
    loadParams: any;
    imageUrl?: string;
    updatedAt?: Millisecond;
    privateMap?: boolean;
};
export declare type CloudUser = {
    name: string;
    email: string;
    thumbnail?: string;
};
export declare type Thumbnail = {
    width: number;
    height: number;
};
export declare type ProviderProps = {
    name?: string;
    displayName?: string;
    icon?: ComponentType<IconProps>;
    thumbnail?: Thumbnail;
};
export interface IconProps {
    height?: string;
    width?: string;
}
export declare const KEPLER_FORMAT = "keplergl";
export declare const FILE_CONFLICT_MSG = "file_conflict";
/**
 * The default provider class
 * @param {object} props
 * @param {string} props.name
 * @param {string} props.displayName
 * @param {React.Component} props.icon - React element
 * @param {object} props.thumbnail - thumbnail size object
 * @param {number} props.thumbnail.width - thumbnail width in pixels
 * @param {number} props.thumbnail.height - thumbnail height in pixels
 * @public
 * @example
 *
 * const myProvider = new Provider({
 *  name: 'foo',
 *  displayName: 'Foo Storage'
 *  icon: Icon,
 *  thumbnail: {width: 300, height: 200}
 * })
 */
export default class Provider {
    name: string;
    displayName: string;
    icon: ComponentType<IconProps>;
    thumbnail: Thumbnail;
    isNew: boolean;
    constructor(props: ProviderProps);
    /**
     * Whether this provider support upload map to a private storage. If truthy, user will be displayed with the storage save icon on the top right of the side bar.
     * @returns
     * @public
     */
    hasPrivateStorage(): boolean;
    /**
     * Whether this provider support share map via a public url, if truthy, user will be displayed with a share map via url under the export map option on the top right of the side bar
     * @returns
     * @public
     */
    hasSharingUrl(): boolean;
    /**
     * This method is called after user share a map, to display the share url.
     * @param fullUrl - Whether to return the full url with domain, or just the location
     * @returns shareUrl
     * @public
     */
    getShareUrl(fullUrl?: boolean): string;
    /**
     * This method is called by kepler.gl demo app to pushes a new location to history, becoming the current location.
     * @returns mapUrl
     * @public
     */
    getMapUrl(loadParams: MapItemLoadParams): string;
    /**
     * This method is called to determine whether user already logged in to this provider
     * @public
     * @returns {Promise<string>} return the access token if a user already logged in
     */
    getAccessToken(): Promise<string | null>;
    /**
     * This method is called to get the user name of the current user. It will be displayed in the cloud provider tile.
     * @public
     * @deprecated please use getUser
     * @returns true if a user already logged in
     */
    getUserName(): string;
    /**
     * return a Promise with the user object
     */
    getUser(): Promise<CloudUser | null>;
    /**
     * This return a standard error that will trigger the overwrite map modal
     */
    getFileConflictError(): Error;
    /**
     * This method will be called when user click the login button in the cloud provider tile.
     * Upon login success and return the user Object {name, email, abbreviated}
     * @public
     */
    login(): Promise<never>;
    /**
     * This method will be called when user click the logout button under the cloud provider tile.
     * Upon login success
     * @public
     */
    logout(): Promise<void>;
    /**
     * This method will be called to upload map for saving and sharing. Kepler.gl will package map data, config, title, description and thumbnail for upload to storage.
     * With the option to overwrite already saved map, and upload as private or public map.
     *
     * @param {Object} param
     * @param {Object} param.mapData - the map object
     * @param {Object} param.mapData.map - {datasets. config, info: {title, description}}
     * @param {Blob} param.mapData.thumbnail - A thumbnail of current map. thumbnail size can be defined by provider by this.thumbnail
     * @param {object} [param.options]
     * @param {boolean} [param.options.overwrite] - whether user choose to overwrite already saved map under the same name
     * @param {boolean} [param.options.isPublic] - whether user wish to share the map with others. if isPublic is truthy, kepler will call this.getShareUrl() to display an URL they can share with others
     * @public
     */
    uploadMap({ mapData, options }: {
        mapData: MapData;
        options: ExportFileOptions;
    }): Promise<MapListItem>;
    /**
     * This method is called to get a list of maps saved by the current logged in user.
     * @returns visualizations an array of Viz objects
     * @public
     * @example
     *  async listMaps() {
     *    return [
     *      {
     *        id: 'a',
     *        title: 'My map',
     *        description: 'My first kepler map',
     *        imageUrl: 'http://',
     *        updatedAt: 1582677787000,
     *        privateMap: false,
     *        loadParams: {}
     *      }
     *    ];
     *  }
     */
    listMaps(): Promise<MapListItem[]>;
    /**
     * This method will be called when user select a map to load from the storage map viewer
     * @param {*} loadParams - the loadParams property of each visualization object
     * @returns mapResponse - the map object containing dataset config info and format option
     * @public
     * @example
     * async downloadMap(loadParams) {
     *  const mockResponse = {
     *    map: {
     *      datasets: [],
     *      config: {},
     *      info: {
     *        app: 'kepler.gl',
     *        created_at: ''
     *        title: 'test map',
     *        description: 'Hello this is my test dropbox map'
     *      }
     *    },
     *    // pass csv here if your provider currently only support save / load file as csv
     *    format: 'keplergl'
     *  };
     *
     *  return downloadMap;
     * }
     */
    downloadMap(loadParams: any): Promise<{
        map: SavedMap;
        format: string;
    }>;
    /**
     * @return {string} return the storage location url for the current provider
     * @public
     */
    getManagementUrl(): string;
}
